# Child GitLab CI Configuration
# This file should be copied to your application repositories as .gitlab-ci.yml
# It references the centralized GitLab CI repository for reusable templates

include:
  # Include templates from the centralized GitLab CI repository
  - project: 'your-group/gitlab-ci'  # Replace with your actual GitLab CI repo path
    ref: main
    file: 
      - 'templates/setup_template.yml'
      - 'templates/build_template.yml'
      - 'templates/deploy_template.yml'
      - 'templates/notify_template.yml'

stages:
  - setup
  - build
  - notify_build
  - deploy
  - notify_final

# Setup stage - prepares authentication
setup:
  extends: .setup_template
  variables:
    GOOGLE_SERVICE_KEY: $SERVICE_ACCOUNT_KEY
  rules:
    - if: $CI_COMMIT_REF_NAME == "dev" || $CI_COMMIT_REF_NAME == "staging" || $CI_COMMIT_REF_NAME == "prod"

# Build stage - builds Docker image from your app's Dockerfile
build:
  extends: .build_template
  variables:
    # Override these variables for your specific application:
    GOOGLE_SERVICE_KEY: $SERVICE_ACCOUNT_KEY
    GAR_HOSTNAME: "us-central1-docker.pkg.dev"
    PROJECT_ID: "$PROJECT_ID"
    REPOSITORY: "ruh"
    IMAGE_NAME: "$CI_PROJECT_NAME"
    REPO_URL: $REPO_URL
  rules:
    - if: $CI_COMMIT_REF_NAME == "dev" || $CI_COMMIT_REF_NAME == "staging" || $CI_COMMIT_REF_NAME == "prod"

# Notification for build failures
slack_notification_build:
  extends: .slack_notification_build
  rules:
    - if: $CI_COMMIT_REF_NAME == "dev" || $CI_COMMIT_REF_NAME == "staging" || $CI_COMMIT_REF_NAME == "prod"
      when: on_failure

# Deploy stage - uses K8s manifest template from centralized repo
deploy:
  extends: .deploy_template
  variables:
    # You can override these variables for specific applications if needed:
    
    # Custom ingress host (optional)
    # INGRESS_HOST: "my-custom-app-$CI_COMMIT_REF_NAME.rapidinnovation.dev"
    
    # Custom resource limits (optional)
    # MEMORY_REQUEST: "128Mi"
    # CPU_REQUEST: "100m" 
    # MEMORY_LIMIT: "2048Mi"
    # CPU_LIMIT: "500m"
    
    # Custom ports (optional)
    # CONTAINER_PORT: "8080"
    # SERVICE_PORT: "80"
  rules:
    - if: $CI_COMMIT_REF_NAME == "dev" || $CI_COMMIT_REF_NAME == "staging" || $CI_COMMIT_REF_NAME == "prod"

# Final notification - always runs to report final status
slack_notification_final:
  extends: .slack_notification_final
  rules:
    - if: $CI_COMMIT_REF_NAME == "dev" || $CI_COMMIT_REF_NAME == "staging" || $CI_COMMIT_REF_NAME == "prod"
      when: always
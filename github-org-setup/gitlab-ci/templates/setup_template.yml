.setup_template:
  stage: setup
  image: google/cloud-sdk
  script:
    - mkdir -p files
    - echo "$SERVICE_ACCOUNT_KEY" > service-account-key.json
    - gcloud auth activate-service-account --key-file=service-account-key.json --project=$PROJECT_ID
    - gcloud auth print-access-token > files/gcloud_token
  artifacts:
    untracked: false
    when: on_success
    paths:
      - files
    expire_in: 1 hr
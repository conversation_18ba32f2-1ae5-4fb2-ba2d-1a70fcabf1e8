.deploy_template:
  stage: deploy
  image: google/cloud-sdk:latest
  variables:
    GAR_HOSTNAME: "us-central1-docker.pkg.dev"
    REPOSITORY: "ruh"
    SERVICE_NAME: "$CI_PROJECT_NAME"
    NAMESPACE: "ruh-$CI_COMMIT_REF_NAME"
    INGRESS_HOST: "$CI_PROJECT_NAME-ruh-$CI_COMMIT_REF_NAME.rapidinnovation.dev"
  # before_script:
  #   - cp templates/k8s-manifest-template.yml k8s-manifest-template.yml
  script:
    # Split complex script into clear steps
    - echo "$SERVICE_ACCOUNT_KEY" > service-account-key.json
    - gcloud auth activate-service-account --key-file=service-account-key.json --project=$PROJECT_ID
    - gcloud container clusters get-credentials $CLUSTER_NAME --region $REGION --project $PROJECT_ID
    
    # Create dynamic K8s manifest
    - cp k8s-manifest-template.yml k8s-manifest-$CI_COMMIT_REF_NAME.yml
    
    # Use explicit line continuation for better readability
    
    - |
      sed -i \
        -e "s|<SERVICE_NAME>|${SERVICE_NAME}|g" \
        -e "s|<NAMESPACE>|${NAMESPACE}|g" \
        -e "s|<GAR_HOSTNAME>|${GAR_HOSTNAME}|g" \
        -e "s|<PROJECT_ID>|${PROJECT_ID}|g" \
        -e "s|<REPOSITORY>|${REPOSITORY}|g" \
        -e "s|<IMAGE_NAME>|${CI_PROJECT_NAME}|g" \
        -e "s|<ENV>|${CI_COMMIT_REF_NAME}|g" \
        -e "s|<VERSION>|${CI_COMMIT_SHORT_SHA}|g" \
        -e "s|<MEMORY_REQUEST>|${MEMORY_REQUEST}|g" \
        -e "s|<CPU_REQUEST>|${CPU_REQUEST}|g" \
        -e "s|<MEMORY_LIMIT>|${MEMORY_LIMIT}|g" \
        -e "s|<CPU_LIMIT>|${CPU_LIMIT}|g" \
        -e "s|<CONTAINER_PORT>|${CONTAINER_PORT}|g" \
        -e "s|<SERVICE_PORT>|${SERVICE_PORT}|g" \
        -e "s|<INGRESS_HOST>|${INGRESS_HOST}|g" \
        "k8s-manifest-${CI_COMMIT_REF_NAME}.yml"
    
    # Check deployment existence
    - DEPLOYMENT_EXISTS=$(kubectl get deployment -n $NAMESPACE $SERVICE_NAME-dp --no-headers --ignore-not-found | wc -l)
    
    # Conditional deployment logic
    - |
      if [ "$DEPLOYMENT_EXISTS" -eq "0" ]; then
        echo "Deployment not found, creating new deployment"
        kubectl apply -f k8s-manifest-$CI_COMMIT_REF_NAME.yml
      else
        echo "Deployment exists, performing rolling update"
        kubectl set image deployment/$SERVICE_NAME-dp $SERVICE_NAME=$GAR_HOSTNAME/$PROJECT_ID/$REPOSITORY/$CI_PROJECT_NAME:$CI_COMMIT_REF_NAME-$CI_COMMIT_SHORT_SHA -n $NAMESPACE
      fi
    - echo "Successfully deployed to GKE"
  after_script:
    - |
      if [ "$CI_JOB_STATUS" = "success" ]; then
        STATUS_EMOJI="✅"
        STATUS_TEXT="succeeded"
      else
        STATUS_EMOJI="❌"
        STATUS_TEXT="failed"
      fi
      echo "CI_JOB_STATUS: $CI_JOB_STATUS"
      echo "CI_PIPELINE_URL: $CI_PIPELINE_URL"
      
      curl -X POST -H 'Content-type: application/json' \
        --data "{\"channel\":\"ruh-cicd\",\"text\":\"${STATUS_EMOJI} Pipeline ${STATUS_TEXT}! Stage: $CI_JOB_STAGE. Check the logs: $CI_PIPELINE_URL\"}" \
        $SLACK_WEBHOOK_SECRET
  
  dependencies:
    - build

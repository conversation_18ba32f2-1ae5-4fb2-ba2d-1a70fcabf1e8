# Centralized GitLab CI Repository

This repository contains reusable GitLab CI templates and Kubernetes manifests for deploying applications across multiple environments (dev, staging, prod).

## Repository Structure

```
├── includes/                    # Stage includes
│   ├── setup.yml               # Setup stage include
│   ├── build.yml               # Build stage include
│   ├── deploy.yml              # Deploy stage include
│   └── notifications.yml       # Notification stages include
├── templates/                   # Reusable templates
│   ├── setup_template.yml      # GCP authentication setup
│   ├── build_template.yml      # Docker build and push
│   ├── deploy_template.yml     # Kubernetes deployment
│   ├── notify_template.yml     # Slack notifications
│   └── k8s-manifest-template.yml # Dynamic K8s manifest
├── child-gitlab-ci.yml         # Example child repo CI config
└── README.md                   # This file
```

## How It Works

### 1. Centralized Templates
- **Setup**: Authenticates with Google Cloud and generates access tokens
- **Build**: Builds Docker images from your application's Dockerfile and pushes to Google Artifact Registry
- **Deploy**: Downloads the K8s manifest template, substitutes variables, and deploys to GKE
- **Notify**: Sends Slack notifications for build failures and final pipeline status

### 2. Dynamic Kubernetes Manifests
The [`k8s-manifest-template.yml`](templates/k8s-manifest-template.yml) contains placeholders that get replaced with actual values:

- `<SERVICE_NAME>` → `$CI_PROJECT_NAME` (your repo name)
- `<NAMESPACE>` → `ruh-$CI_COMMIT_REF_NAME` (e.g., ruh-dev, ruh-staging, ruh-prod)
- `<PROJECT_ID>` → Your GCP project ID
- `<IMAGE_NAME>` → Your application name
- `<ENV>` → Branch name (dev/staging/prod)
- `<VERSION>` → Git commit SHA

### 3. Multi-Environment Support
Supports three environments based on Git branches:
- **dev** → `ruh-dev` namespace
- **staging** → `ruh-staging` namespace  
- **prod** → `ruh-prod` namespace

## Setup Instructions

### 1. Update Repository References
In [`templates/deploy_template.yml`](templates/deploy_template.yml), update line 13:
```yaml
- curl -H "PRIVATE-TOKEN: $CI_JOB_TOKEN" "$CI_API_V4_URL/projects/your-group%2Fgitlab-ci/repository/files/templates%2Fk8s-manifest-template.yml/raw?ref=main" -o k8s-manifest-template.yml
```
Replace `your-group%2Fgitlab-ci` with your actual GitLab group and repository path.

### 2. Configure Child Repositories
Copy [`child-gitlab-ci.yml`](child-gitlab-ci.yml) to your application repositories as `.gitlab-ci.yml` and update:

```yaml
include:
  - project: 'your-group/gitlab-ci'  # Update this path
    ref: main
    file: 
      - 'templates/setup_template.yml'
      - 'templates/build_template.yml'
      - 'templates/deploy_template.yml'
      - 'templates/notify_template.yml'
```

### 3. Required GitLab CI Variables
Set these variables in your child repositories:

#### Required Variables:
- `SERVICE_ACCOUNT_KEY` - GCP service account JSON key
- `PROJECT_ID` - GCP project ID
- `CLUSTER_NAME` - GKE cluster name
- `REGION` - GCP region
- `SLACK_WEBHOOK_SECRET` - Slack webhook URL
- `REPO_URL` - Git repository URL (if needed for build args)
- `GIT_TOKEN` - Git access token (if needed for private repos)

#### Optional Variables (can be overridden in child repos):
- `INGRESS_HOST` - Custom ingress hostname
- `MEMORY_REQUEST` - Pod memory request (default: 64Mi)
- `CPU_REQUEST` - Pod CPU request (default: 50m)
- `MEMORY_LIMIT` - Pod memory limit (default: 1024Mi)
- `CPU_LIMIT` - Pod CPU limit (default: 250m)
- `CONTAINER_PORT` - Application port (default: 50059)
- `SERVICE_PORT` - Service port (default: 80)

## Usage Example

### Application Repository Structure
Your application repositories only need:
```
├── Dockerfile              # Your application's Docker build
├── .gitlab-ci.yml          # Copy of child-gitlab-ci.yml
└── src/                    # Your application code
```

### Pipeline Flow
1. **Setup**: Authenticates with GCP
2. **Build**: Builds Docker image from your Dockerfile
3. **Deploy**: 
   - Downloads K8s manifest template from this repo
   - Substitutes variables (service name, namespace, image, etc.)
   - Deploys to appropriate GKE namespace
4. **Notify**: Sends Slack notifications

### Generated Resources
For a project named `my-api` on `dev` branch:
- **ServiceAccount**: `my-api-sa` in `ruh-dev` namespace
- **Deployment**: `my-api-dp` in `ruh-dev` namespace
- **Service**: `my-api-svc` in `ruh-dev` namespace
- **Ingress**: `my-api-ingress` with host `my-api-ruh-dev.rapidinnovation.dev`

## Customization

### Override Default Values
In your child repository's `.gitlab-ci.yml`, you can override defaults:

```yaml
deploy:
  extends: .deploy_template
  variables:
    INGRESS_HOST: "custom-api-$CI_COMMIT_REF_NAME.mydomain.com"
    MEMORY_LIMIT: "2048Mi"
    CPU_LIMIT: "500m"
    CONTAINER_PORT: "8080"
```

### Add Custom Build Args
Modify the build template variables in your child repo:

```yaml
build:
  extends: .build_template
  variables:
    CUSTOM_BUILD_ARG: "value"
```

## Benefits

1. **DRY Principle**: Write once, use everywhere
2. **Consistency**: Same deployment patterns across all applications
3. **Maintainability**: Update templates in one place
4. **Flexibility**: Override defaults per application
5. **Multi-Environment**: Automatic namespace and resource naming
6. **Dynamic**: Service names and resources based on repository name

## Troubleshooting

### Common Issues:
1. **Template not found**: Verify the project path in include section
2. **Authentication failed**: Check SERVICE_ACCOUNT_KEY variable
3. **Deployment failed**: Verify CLUSTER_NAME and REGION variables
4. **Image not found**: Ensure build stage completed successfully

### Debug Commands:
```bash
# Check if deployment exists
kubectl get deployment -n ruh-dev my-api-dp

# Check pod logs
kubectl logs -n ruh-dev deployment/my-api-dp

# Check service
kubectl get svc -n ruh-dev my-api-svc
#!/bin/bash

# GitHub Actions Variables Setup Script
# This script sets up organization variables and secrets for GitHub Actions

set -e

# Configuration - UPDATE THESE VALUES
ORG="your-org"  # Replace with your GitHub organization name
TEAMS=("ruh-catalyst" "ruh-aim" "ruh-commons")

# Global configuration
GAR_HOSTNAME="us-central1-docker.pkg.dev"
GCP_PROJECT_ID="your-gcp-project-id"  # Replace with your GCP project ID
GCP_REGION="us-central1"
REPO_URL="https://github.com/${ORG}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if gh CLI is installed
    if ! command -v gh &> /dev/null; then
        log_error "GitHub CLI (gh) is not installed. Please install it first."
        exit 1
    fi
    
    # Check if user is authenticated
    if ! gh auth status &> /dev/null; then
        log_error "Not authenticated with GitHub CLI. Please run 'gh auth login' first."
        exit 1
    fi
    
    # Check if user has org admin access
    if ! gh api orgs/${ORG} &> /dev/null; then
        log_error "Cannot access organization '${ORG}'. Please check permissions."
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

set_global_variables() {
    log_info "Setting global organization variables..."
    
    gh variable set GAR_HOSTNAME --body "${GAR_HOSTNAME}" --org ${ORG}
    log_success "Set GAR_HOSTNAME = ${GAR_HOSTNAME}"
}

set_team_variables() {
    local team=$1
    local team_upper=$(echo $team | tr '[:lower:]' '[:upper:]' | tr '-' '_')
    
    log_info "Setting variables for team: ${team}"
    
    # Set team-specific variables
    gh variable set "${team_upper}_PROJECT_ID" --body "${GCP_PROJECT_ID}" --org ${ORG}
    gh variable set "${team_upper}_REGION" --body "${GCP_REGION}" --org ${ORG}
    gh variable set "${team_upper}_CLUSTER_NAME" --body "${team}-cluster" --org ${ORG}
    gh variable set "${team_upper}_REPOSITORY" --body "${team}" --org ${ORG}
    gh variable set "${team_upper}_REPO_URL" --body "${REPO_URL}" --org ${ORG}
    
    log_success "Set variables for ${team} team"
}

create_github_teams() {
    log_info "Creating GitHub teams..."
    
    for team in "${TEAMS[@]}"; do
        if gh api orgs/${ORG}/teams/${team} &> /dev/null; then
            log_warning "Team '${team}' already exists"
        else
            gh api orgs/${ORG}/teams -f name="${team}" -f description="RUH ${team} Team"
            log_success "Created team: ${team}"
        fi
    done
}

display_secrets_instructions() {
    log_info "Manual secrets setup required..."
    echo
    echo "Please set the following secrets manually in GitHub:"
    echo "Organization Settings > Secrets and variables > Actions > Secrets"
    echo
    
    echo -e "${YELLOW}Global Secrets:${NC}"
    echo "  GIT_TOKEN=<github-personal-access-token>"
    echo
    
    for team in "${TEAMS[@]}"; do
        local team_upper=$(echo $team | tr '[:lower:]' '[:upper:]' | tr '-' '_')
        echo -e "${YELLOW}${team} Team Secrets:${NC}"
        echo "  ${team_upper}_SERVICE_ACCOUNT_KEY=<base64-encoded-service-account-key>"
        echo "  ${team_upper}_SLACK_WEBHOOK=<slack-webhook-url>"
        echo
    done
    
    echo -e "${BLUE}To set secrets via CLI:${NC}"
    echo "gh secret set SECRET_NAME --body 'secret-value' --org ${ORG}"
}

verify_setup() {
    log_info "Verifying setup..."
    
    echo
    echo -e "${BLUE}Organization Variables:${NC}"
    gh variable list --org ${ORG}
    
    echo
    echo -e "${BLUE}Organization Secrets:${NC}"
    gh secret list --org ${ORG}
    
    echo
    echo -e "${BLUE}GitHub Teams:${NC}"
    for team in "${TEAMS[@]}"; do
        if gh api orgs/${ORG}/teams/${team} &> /dev/null; then
            echo "  ✅ ${team}"
        else
            echo "  ❌ ${team}"
        fi
    done
}

main() {
    echo "🚀 GitHub Actions Setup Script"
    echo "Organization: ${ORG}"
    echo "Teams: ${TEAMS[*]}"
    echo
    
    # Confirm before proceeding
    read -p "Do you want to proceed with the setup? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Setup cancelled"
        exit 0
    fi
    
    check_prerequisites
    
    # Set global variables
    set_global_variables
    
    # Set team-specific variables
    for team in "${TEAMS[@]}"; do
        set_team_variables $team
    done
    
    # Create GitHub teams
    create_github_teams
    
    # Display instructions for manual secret setup
    display_secrets_instructions
    
    # Verify setup
    verify_setup
    
    echo
    log_success "Setup completed successfully!"
    echo
    echo "Next steps:"
    echo "1. Set up the secrets manually as shown above"
    echo "2. Create your first workflow using the examples in examples/"
    echo "3. Test the workflow with a simple service"
    echo "4. Refer to docs/setup-guide.md for detailed instructions"
}

# Run main function
main "$@"

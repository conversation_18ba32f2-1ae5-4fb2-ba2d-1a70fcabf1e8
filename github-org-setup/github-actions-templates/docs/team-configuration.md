# Team-Based Configuration Guide

This guide explains how to set up team-based variables and secrets in GitHub to mirror your GitLab group structure.

## 🏢 GitHub Organization Setup

### 1. Create GitHub Teams

First, create teams in your GitHub organization that correspond to your GitLab groups:

```bash
# Using GitHub CLI (gh)
gh api orgs/YOUR_ORG/teams -f name="ruh-catalyst" -f description="RUH Catalyst Team"
gh api orgs/YOUR_ORG/teams -f name="ruh-aim" -f description="RUH AIM Team"  
gh api orgs/YOUR_ORG/teams -f name="ruh-commons" -f description="RUH Commons Team"
```

Or create them through the GitHub web interface:
1. Go to your organization settings
2. Navigate to "Teams"
3. Click "New team"
4. Create teams: `ruh-catalyst`, `ruh-aim`, `ruh-commons`

### 2. Organization Variables

Set up organization-level variables that correspond to your GitLab group variables.

#### Global Variables (Organization Level)

Navigate to: `Organization Settings > Secrets and variables > Actions > Variables`

```
GAR_HOSTNAME=us-central1-docker.pkg.dev
```

#### Team-Specific Variables

**For RUH Catalyst Team:**
```
RUH_CATALYST_PROJECT_ID=your-gcp-project-id
RUH_CATALYST_REGION=us-central1
RUH_CATALYST_CLUSTER_NAME=ruh-catalyst-cluster
RUH_CATALYST_REPOSITORY=ruh-catalyst
RUH_CATALYST_REPO_URL=https://github.com/your-org
```

**For RUH AIM Team:**
```
RUH_AIM_PROJECT_ID=your-gcp-project-id
RUH_AIM_REGION=us-central1
RUH_AIM_CLUSTER_NAME=ruh-aim-cluster
RUH_AIM_REPOSITORY=ruh-aim
RUH_AIM_REPO_URL=https://github.com/your-org
```

**For RUH Commons Team:**
```
RUH_COMMONS_PROJECT_ID=your-gcp-project-id
RUH_COMMONS_REGION=us-central1
RUH_COMMONS_CLUSTER_NAME=ruh-commons-cluster
RUH_COMMONS_REPOSITORY=ruh-commons
RUH_COMMONS_REPO_URL=https://github.com/your-org
```

### 3. Organization Secrets

Navigate to: `Organization Settings > Secrets and variables > Actions > Secrets`

#### Global Secrets
```
GIT_TOKEN=<github-personal-access-token>
```

#### Team-Specific Secrets

**For RUH Catalyst Team:**
```
RUH_CATALYST_SERVICE_ACCOUNT_KEY=<base64-encoded-service-account-key>
RUH_CATALYST_SLACK_WEBHOOK=<slack-webhook-url>
```

**For RUH AIM Team:**
```
RUH_AIM_SERVICE_ACCOUNT_KEY=<base64-encoded-service-account-key>
RUH_AIM_SLACK_WEBHOOK=<slack-webhook-url>
```

**For RUH Commons Team:**
```
RUH_COMMONS_SERVICE_ACCOUNT_KEY=<base64-encoded-service-account-key>
RUH_COMMONS_SLACK_WEBHOOK=<slack-webhook-url>
```

## 🔄 GitLab to GitHub Mapping

| GitLab Concept | GitHub Equivalent | Description |
|----------------|-------------------|-------------|
| GitLab Groups | GitHub Teams | Organizational units |
| Group Variables | Organization Variables | Team-specific configuration |
| Group CI/CD Variables | Organization Secrets | Sensitive team data |
| `.gitlab-ci.yml` includes | Reusable workflows | Template sharing |
| GitLab Runners | GitHub-hosted runners | Execution environment |

## 🎯 Variable Naming Convention

### GitLab Group Variables (Current)
```
CLUSTER_NAME
GIT_TOKEN
PROJECT_ID
REGION
REPOSITORY
REPO_URL
SERVICE_ACCOUNT_KEY
```

### GitHub Organization Variables (New)
```
# Team-prefixed variables
{TEAM}_CLUSTER_NAME
{TEAM}_PROJECT_ID
{TEAM}_REGION
{TEAM}_REPOSITORY
{TEAM}_REPO_URL

# Team-prefixed secrets
{TEAM}_SERVICE_ACCOUNT_KEY
{TEAM}_SLACK_WEBHOOK

# Global variables/secrets
GIT_TOKEN
GAR_HOSTNAME
```

Where `{TEAM}` is one of: `RUH_CATALYST`, `RUH_AIM`, `RUH_COMMONS`

## 🚀 Usage in Workflows

### Team-Aware Pipeline
```yaml
jobs:
  ci-cd:
    uses: your-org/github-actions-templates/.github/workflows/team-pipeline.yml@main
    with:
      service_name: 'my-service'
      team: 'ruh-catalyst'  # Automatically uses RUH_CATALYST_* variables
      memory_request: '200Mi'
      cpu_request: '100m'
      memory_limit: '600Mi'
      cpu_limit: '250m'
      container_port: '8080'
      service_port: '80'
    secrets:
      git_token: ${{ secrets.GIT_TOKEN }}
      team_service_account_key: ${{ secrets.RUH_CATALYST_SERVICE_ACCOUNT_KEY }}
      team_slack_webhook: ${{ secrets.RUH_CATALYST_SLACK_WEBHOOK }}
```

### Basic Pipeline (Manual Configuration)
```yaml
jobs:
  ci-cd:
    uses: your-org/github-actions-templates/.github/workflows/main-pipeline.yml@main
    with:
      service_name: 'my-service'
      namespace_prefix: 'ruh-catalyst'
      project_id: ${{ vars.RUH_CATALYST_PROJECT_ID }}
      region: ${{ vars.RUH_CATALYST_REGION }}
      cluster_name: ${{ vars.RUH_CATALYST_CLUSTER_NAME }}
      repository: ${{ vars.RUH_CATALYST_REPOSITORY }}
      repo_url: ${{ vars.RUH_CATALYST_REPO_URL }}
    secrets:
      git_token: ${{ secrets.GIT_TOKEN }}
      service_account_key: ${{ secrets.RUH_CATALYST_SERVICE_ACCOUNT_KEY }}
      slack_webhook: ${{ secrets.RUH_CATALYST_SLACK_WEBHOOK }}
```

## 🔧 Environment-Specific Configuration

### Namespace Pattern
- **GitLab**: `ruh-$CI_COMMIT_REF_NAME` → `ruh-dev`, `ruh-staging`, `ruh-main`
- **GitHub**: `{team}-${{ github.ref_name }}` → `ruh-catalyst-dev`, `ruh-aim-staging`, `ruh-commons-main`

### Ingress Host Pattern
- **GitLab**: `$CI_PROJECT_NAME-ruh-$CI_COMMIT_REF_NAME.rapidinnovation.dev`
- **GitHub**: `${{ inputs.service_name }}-{team}-${{ github.ref_name }}.rapidinnovation.dev`

### Docker Image Tagging
- **GitLab**: `$CI_PROJECT_NAME:${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHORT_SHA}`
- **GitHub**: `${{ inputs.service_name }}:${{ github.ref_name }}-${{ github.sha }}`

## 🛠️ Setup Script

You can use this script to set up variables programmatically:

```bash
#!/bin/bash
# setup-github-variables.sh

ORG="your-org"
TEAMS=("ruh-catalyst" "ruh-aim" "ruh-commons")

# Set global variables
gh variable set GAR_HOSTNAME --body "us-central1-docker.pkg.dev" --org $ORG

# Set team-specific variables
for team in "${TEAMS[@]}"; do
    TEAM_UPPER=$(echo $team | tr '[:lower:]' '[:upper:]' | tr '-' '_')
    
    # Variables
    gh variable set "${TEAM_UPPER}_PROJECT_ID" --body "your-gcp-project-id" --org $ORG
    gh variable set "${TEAM_UPPER}_REGION" --body "us-central1" --org $ORG
    gh variable set "${TEAM_UPPER}_CLUSTER_NAME" --body "${team}-cluster" --org $ORG
    gh variable set "${TEAM_UPPER}_REPOSITORY" --body "$team" --org $ORG
    gh variable set "${TEAM_UPPER}_REPO_URL" --body "https://github.com/$ORG" --org $ORG
    
    # Secrets (you'll need to provide actual values)
    echo "Please set these secrets manually:"
    echo "  ${TEAM_UPPER}_SERVICE_ACCOUNT_KEY"
    echo "  ${TEAM_UPPER}_SLACK_WEBHOOK"
done

# Global secrets
echo "Please set these global secrets manually:"
echo "  GIT_TOKEN"
```

## 🔍 Verification

After setup, verify your configuration:

1. Check organization variables: `gh variable list --org YOUR_ORG`
2. Check organization secrets: `gh secret list --org YOUR_ORG`
3. Test a workflow run to ensure variables are accessible

## 🚨 Security Considerations

1. **Service Account Keys**: Store as secrets, not variables
2. **Slack Webhooks**: Store as secrets, not variables  
3. **Git Tokens**: Use fine-grained personal access tokens
4. **Team Access**: Ensure teams only have access to their resources
5. **Environment Protection**: Consider using GitHub environments for production deployments

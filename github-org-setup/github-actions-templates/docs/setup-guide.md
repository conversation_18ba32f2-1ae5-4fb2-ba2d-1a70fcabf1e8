# GitHub Actions Setup Guide

This guide walks you through setting up the GitHub Actions templates to replace your GitLab CI setup.

## 📋 Prerequisites

- GitHub organization with admin access
- Google Cloud Platform project with GKE cluster
- Kubernetes cluster configured and accessible
- Docker registry (Google Artifact Registry) set up
- Slack workspace for notifications

## 🚀 Quick Setup

### Step 1: Create the Templates Repository

1. Create a new repository in your organization: `github-actions-templates`
2. Copy all files from this template into the repository
3. Update the organization name in all example files

### Step 2: Set Up Organization Variables and Secrets

Run the setup script or manually configure:

```bash
# Clone the setup script
curl -O https://raw.githubusercontent.com/your-org/github-actions-templates/main/scripts/setup-variables.sh
chmod +x setup-variables.sh

# Edit the script with your values
vim setup-variables.sh

# Run the setup
./setup-variables.sh
```

### Step 3: Configure Your First Service

1. In your service repository, create `.github/workflows/ci-cd.yml`
2. Copy one of the examples from the `examples/` folder
3. Customize the configuration for your service
4. Commit and push to trigger the workflow

## 🔧 Detailed Configuration

### Organization Variables

Navigate to: `Organization Settings > Secrets and variables > Actions > Variables`

#### Global Variables
```
GAR_HOSTNAME=us-central1-docker.pkg.dev
```

#### Team-Specific Variables

**RUH Catalyst Team:**
```
RUH_CATALYST_PROJECT_ID=your-gcp-project-id
RUH_CATALYST_REGION=us-central1
RUH_CATALYST_CLUSTER_NAME=ruh-catalyst-cluster
RUH_CATALYST_REPOSITORY=ruh-catalyst
RUH_CATALYST_REPO_URL=https://github.com/your-org
```

**RUH AIM Team:**
```
RUH_AIM_PROJECT_ID=your-gcp-project-id
RUH_AIM_REGION=us-central1
RUH_AIM_CLUSTER_NAME=ruh-aim-cluster
RUH_AIM_REPOSITORY=ruh-aim
RUH_AIM_REPO_URL=https://github.com/your-org
```

**RUH Commons Team:**
```
RUH_COMMONS_PROJECT_ID=your-gcp-project-id
RUH_COMMONS_REGION=us-central1
RUH_COMMONS_CLUSTER_NAME=ruh-commons-cluster
RUH_COMMONS_REPOSITORY=ruh-commons
RUH_COMMONS_REPO_URL=https://github.com/your-org
```

### Organization Secrets

Navigate to: `Organization Settings > Secrets and variables > Actions > Secrets`

#### Global Secrets
```
GIT_TOKEN=<github-personal-access-token>
```

#### Team-Specific Secrets
```
RUH_CATALYST_SERVICE_ACCOUNT_KEY=<base64-encoded-service-account-key>
RUH_CATALYST_SLACK_WEBHOOK=<slack-webhook-url>

RUH_AIM_SERVICE_ACCOUNT_KEY=<base64-encoded-service-account-key>
RUH_AIM_SLACK_WEBHOOK=<slack-webhook-url>

RUH_COMMONS_SERVICE_ACCOUNT_KEY=<base64-encoded-service-account-key>
RUH_COMMONS_SLACK_WEBHOOK=<slack-webhook-url>
```

## 🏗️ Workflow Types

### 1. Team-Aware Pipeline (Recommended)

**File:** `.github/workflows/team-pipeline.yml`

**Best for:**
- Standard team-based services
- Automatic team configuration
- Consistent deployments

**Example:**
```yaml
jobs:
  ci-cd:
    uses: your-org/github-actions-templates/.github/workflows/team-pipeline.yml@main
    with:
      service_name: "my-service"
      team: "ruh-catalyst"
      memory_request: "200Mi"
      cpu_request: "100m"
      memory_limit: "600Mi"
      cpu_limit: "250m"
      container_port: "8080"
      service_port: "80"
    secrets:
      git_token: ${{ secrets.GIT_TOKEN }}
      team_service_account_key: ${{ secrets.RUH_CATALYST_SERVICE_ACCOUNT_KEY }}
      team_slack_webhook: ${{ secrets.RUH_CATALYST_SLACK_WEBHOOK }}
```

### 2. Basic Pipeline

**File:** `.github/workflows/main-pipeline.yml`

**Best for:**
- Custom configurations
- Mixed team deployments
- Explicit variable control

**Example:**
```yaml
jobs:
  ci-cd:
    uses: your-org/github-actions-templates/.github/workflows/main-pipeline.yml@main
    with:
      service_name: "my-service"
      namespace_prefix: "ruh-catalyst"
      project_id: ${{ vars.RUH_CATALYST_PROJECT_ID }}
      region: ${{ vars.RUH_CATALYST_REGION }}
      cluster_name: ${{ vars.RUH_CATALYST_CLUSTER_NAME }}
      repository: ${{ vars.RUH_CATALYST_REPOSITORY }}
      repo_url: ${{ vars.RUH_CATALYST_REPO_URL }}
    secrets:
      git_token: ${{ secrets.GIT_TOKEN }}
      service_account_key: ${{ secrets.RUH_CATALYST_SERVICE_ACCOUNT_KEY }}
      slack_webhook: ${{ secrets.RUH_CATALYST_SLACK_WEBHOOK }}
```

### 3. Individual Component Workflows

You can also use individual components:

```yaml
jobs:
  setup:
    uses: your-org/github-actions-templates/.github/workflows/setup.yml@main
    
  build:
    needs: setup
    uses: your-org/github-actions-templates/.github/workflows/build.yml@main
    
  deploy:
    needs: build
    uses: your-org/github-actions-templates/.github/workflows/deploy.yml@main
```

## 🔍 Testing Your Setup

### 1. Validate Variables
```bash
# Check if variables are set
gh variable list --org YOUR_ORG

# Check if secrets are set
gh secret list --org YOUR_ORG
```

### 2. Test Workflow
1. Create a simple test service
2. Add a basic workflow file
3. Push to trigger the workflow
4. Check the Actions tab for results

### 3. Verify Kubernetes Resources
```bash
# Check if namespace was created
kubectl get namespaces | grep ruh-

# Check if deployment was created
kubectl get deployments -n ruh-catalyst-dev

# Check if service was created
kubectl get services -n ruh-catalyst-dev

# Check if ingress was created
kubectl get ingress -n ruh-catalyst-dev
```

## 🚨 Troubleshooting

### Common Issues

**1. Variables not found**
- Verify organization variables are set correctly
- Check variable names match exactly (case-sensitive)
- Ensure repository has access to organization variables

**2. Authentication failed**
- Verify service account key is valid and base64 encoded
- Check GCP project permissions
- Ensure cluster exists and is accessible

**3. Build failed**
- Check Dockerfile exists in repository root
- Verify build arguments are correct
- Check Docker registry permissions

**4. Deployment failed**
- Verify Kubernetes cluster is accessible
- Check namespace permissions
- Verify image was pushed successfully

**5. Notifications not working**
- Check Slack webhook URL is correct
- Verify webhook has proper permissions
- Check Slack channel exists

### Debug Commands

```bash
# Check workflow logs
gh run list --repo your-org/your-service
gh run view RUN_ID --repo your-org/your-service

# Check Kubernetes resources
kubectl describe deployment SERVICE_NAME-dp -n NAMESPACE
kubectl logs -l app=SERVICE_NAME -n NAMESPACE

# Check Docker image
docker pull IMAGE_NAME
docker inspect IMAGE_NAME
```

## 📚 Next Steps

1. **Migrate Services**: Start with one service per team
2. **Test Thoroughly**: Verify all environments work correctly
3. **Update Documentation**: Document any team-specific customizations
4. **Train Teams**: Ensure all team members understand the new workflow
5. **Monitor**: Set up monitoring for the new deployment pipeline

## 🔗 Additional Resources

- [GitHub Actions Documentation](https://docs.github.com/en/actions)
- [Reusable Workflows](https://docs.github.com/en/actions/using-workflows/reusing-workflows)
- [Organization Variables and Secrets](https://docs.github.com/en/actions/learn-github-actions/variables)
- [Google Kubernetes Engine](https://cloud.google.com/kubernetes-engine/docs)
- [Kubernetes Deployments](https://kubernetes.io/docs/concepts/workloads/controllers/deployment/)

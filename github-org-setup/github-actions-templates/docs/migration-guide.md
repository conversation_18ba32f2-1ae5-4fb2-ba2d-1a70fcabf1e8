# GitLab CI to GitHub Actions Migration Guide

This guide helps you migrate from your existing GitLab CI setup to GitHub Actions while maintaining the same functionality and team structure.

## 🔄 Migration Overview

### What We're Migrating

**From GitLab:**
- GitLab Groups → GitHub Teams
- GitLab CI Templates → GitHub Actions Reusable Workflows
- Group Variables → Organization Variables
- Group CI/CD Variables → Organization Secrets
- `.gitlab-ci.yml` → `.github/workflows/ci-cd.yml`

### Migration Benefits

- **Unified Platform**: Code and CI/CD in one place
- **Better Integration**: Native GitHub features
- **Improved Security**: Fine-grained access controls
- **Cost Optimization**: GitHub-hosted runners
- **Enhanced Collaboration**: Pull request workflows

## 📊 Feature Mapping

| GitLab CI Feature | GitHub Actions Equivalent | Status |
|-------------------|---------------------------|---------|
| `.setup_template` | `setup.yml` workflow | ✅ Complete |
| `.build_template` | `build.yml` workflow | ✅ Complete |
| `.deploy_template` | `deploy.yml` workflow | ✅ Complete |
| `.slack_notification_*` | `notify.yml` workflow | ✅ Complete |
| Group variables | Organization variables | ✅ Complete |
| Group CI/CD variables | Organization secrets | ✅ Complete |
| Dynamic namespaces | Team-based namespaces | ✅ Complete |
| Multi-environment | Branch-based deployment | ✅ Complete |
| Resource limits | Configurable resources | ✅ Complete |
| Ingress configuration | Automatic ingress setup | ✅ Complete |

## 🚀 Step-by-Step Migration

### Phase 1: Setup GitHub Actions Infrastructure

#### 1.1 Create Templates Repository
```bash
# Create the templates repository
gh repo create your-org/github-actions-templates --public

# Clone and set up
git clone https://github.com/your-org/github-actions-templates.git
cd github-actions-templates

# Copy template files (from this guide)
# ... copy all template files ...

git add .
git commit -m "Initial GitHub Actions templates"
git push origin main
```

#### 1.2 Set Up Organization Variables
```bash
# Use the provided setup script
./scripts/setup-variables.sh

# Or manually set variables in GitHub UI
# Organization Settings > Secrets and variables > Actions
```

#### 1.3 Create GitHub Teams
```bash
# Create teams matching GitLab groups
gh api orgs/YOUR_ORG/teams -f name="ruh-catalyst" -f description="RUH Catalyst Team"
gh api orgs/YOUR_ORG/teams -f name="ruh-aim" -f description="RUH AIM Team"
gh api orgs/YOUR_ORG/teams -f name="ruh-commons" -f description="RUH Commons Team"
```

### Phase 2: Migrate Individual Services

#### 2.1 Choose a Pilot Service
Start with a non-critical service from each team:
- **RUH Catalyst**: `node-executor-service` (already configured)
- **RUH AIM**: Simple AI service
- **RUH Commons**: Authentication service

#### 2.2 Create GitHub Actions Workflow

**For node-executor-service:**
```yaml
# .github/workflows/ci-cd.yml
name: Node Executor Service CI/CD

on:
  push:
    branches: [main, dev, staging]
  workflow_dispatch:

jobs:
  ci-cd:
    uses: your-org/github-actions-templates/.github/workflows/team-pipeline.yml@main
    with:
      service_name: "node-executor-service"
      team: "ruh-catalyst"
      memory_request: "200Mi"
      cpu_request: "100m"
      memory_limit: "600Mi"
      cpu_limit: "250m"
      container_port: "50052"
      service_port: "80"
    secrets:
      git_token: ${{ secrets.GIT_TOKEN }}
      team_service_account_key: ${{ secrets.RUH_CATALYST_SERVICE_ACCOUNT_KEY }}
      team_slack_webhook: ${{ secrets.RUH_CATALYST_SLACK_WEBHOOK }}
```

#### 2.3 Test the Migration
1. Push the workflow file
2. Trigger a build on `dev` branch
3. Verify deployment in Kubernetes
4. Test the application functionality
5. Check Slack notifications

### Phase 3: Parallel Operation

Run both GitLab CI and GitHub Actions in parallel:

#### 3.1 Disable GitLab CI Deployment
Modify `.gitlab-ci.yml` to only build, not deploy:
```yaml
# Comment out or remove deploy stage
# deploy:
#   extends: .deploy_template
```

#### 3.2 Enable GitHub Actions Deployment
Ensure GitHub Actions handles deployment:
```yaml
# GitHub Actions workflow handles deployment
on:
  push:
    branches: [main, dev, staging]
```

#### 3.3 Monitor Both Systems
- Compare build times
- Verify deployment consistency
- Monitor application performance
- Check notification delivery

### Phase 4: Full Migration

#### 4.1 Migrate All Services
For each service:
1. Create GitHub Actions workflow
2. Test thoroughly
3. Disable GitLab CI
4. Monitor for issues

#### 4.2 Update Documentation
- Update deployment procedures
- Update troubleshooting guides
- Update team onboarding docs

#### 4.3 Clean Up GitLab CI
- Archive GitLab CI repository
- Remove unused GitLab runners
- Clean up GitLab group variables

## 🔧 Configuration Migration

### Variable Mapping

| GitLab Group Variable | GitHub Organization Variable | Notes |
|----------------------|------------------------------|-------|
| `CLUSTER_NAME` | `{TEAM}_CLUSTER_NAME` | Team-prefixed |
| `GIT_TOKEN` | `GIT_TOKEN` | Global secret |
| `PROJECT_ID` | `{TEAM}_PROJECT_ID` | Team-prefixed |
| `REGION` | `{TEAM}_REGION` | Team-prefixed |
| `REPOSITORY` | `{TEAM}_REPOSITORY` | Team-prefixed |
| `REPO_URL` | `{TEAM}_REPO_URL` | Team-prefixed |
| `SERVICE_ACCOUNT_KEY` | `{TEAM}_SERVICE_ACCOUNT_KEY` | Team-prefixed secret |

### Namespace Migration

**GitLab Pattern:**
```
ruh-$CI_COMMIT_REF_NAME
# Examples: ruh-dev, ruh-staging, ruh-main
```

**GitHub Pattern:**
```
{team}-${{ github.ref_name }}
# Examples: ruh-catalyst-dev, ruh-aim-staging, ruh-commons-main
```

### Image Tagging Migration

**GitLab Pattern:**
```
$CI_PROJECT_NAME:${CI_COMMIT_REF_NAME}-${CI_COMMIT_SHORT_SHA}
# Example: node-executor-service:dev-a1b2c3d4
```

**GitHub Pattern:**
```
${{ inputs.service_name }}:${{ github.ref_name }}-${{ github.sha }}
# Example: node-executor-service:dev-a1b2c3d4f5e6f7g8h9i0j1k2l3m4n5o6p7q8r9s0
```

## 🚨 Common Migration Issues

### Issue 1: Variable Not Found
**Problem:** GitHub Actions can't find organization variables
**Solution:**
```bash
# Check variable exists
gh variable list --org YOUR_ORG | grep VARIABLE_NAME

# Set if missing
gh variable set VARIABLE_NAME --body "value" --org YOUR_ORG
```

### Issue 2: Different SHA Format
**Problem:** GitHub SHA is longer than GitLab short SHA
**Solution:** Use substring in workflows:
```yaml
# Use first 8 characters like GitLab
image_tag: "${{ github.ref_name }}-${{ github.sha:0:8 }}"
```

### Issue 3: Namespace Conflicts
**Problem:** New namespace pattern conflicts with existing deployments
**Solution:** 
1. Plan namespace migration carefully
2. Use blue-green deployment strategy
3. Update ingress configurations

### Issue 4: Build Context Differences
**Problem:** Different build context between GitLab and GitHub
**Solution:**
```dockerfile
# Ensure Dockerfile works in both contexts
COPY . /app
WORKDIR /app
```

## 📈 Migration Timeline

### Week 1: Infrastructure Setup
- [ ] Create GitHub Actions templates repository
- [ ] Set up organization variables and secrets
- [ ] Create GitHub teams
- [ ] Test basic workflow

### Week 2: Pilot Migration
- [ ] Migrate one service per team
- [ ] Test thoroughly in all environments
- [ ] Gather team feedback
- [ ] Refine templates

### Week 3-4: Parallel Operation
- [ ] Run both systems in parallel
- [ ] Compare performance and reliability
- [ ] Train teams on new workflows
- [ ] Update documentation

### Week 5-6: Full Migration
- [ ] Migrate remaining services
- [ ] Disable GitLab CI
- [ ] Monitor for issues
- [ ] Clean up old infrastructure

### Week 7: Optimization
- [ ] Optimize workflow performance
- [ ] Implement advanced features
- [ ] Set up monitoring and alerting
- [ ] Conduct retrospective

## ✅ Migration Checklist

### Pre-Migration
- [ ] GitHub organization admin access
- [ ] All required variables documented
- [ ] Service account keys available
- [ ] Slack webhooks configured
- [ ] Team members notified

### During Migration
- [ ] Templates repository created
- [ ] Organization variables set
- [ ] Organization secrets configured
- [ ] GitHub teams created
- [ ] Pilot service migrated
- [ ] Parallel operation tested

### Post-Migration
- [ ] All services migrated
- [ ] GitLab CI disabled
- [ ] Documentation updated
- [ ] Teams trained
- [ ] Monitoring configured
- [ ] Old infrastructure cleaned up

## 🎯 Success Metrics

- **Migration Speed**: All services migrated within 6 weeks
- **Reliability**: 99.9% deployment success rate
- **Performance**: Build times improved by 20%
- **Team Satisfaction**: Positive feedback from all teams
- **Cost Reduction**: 30% reduction in CI/CD costs

## 📞 Support

For migration support:
- **Documentation**: Check this guide and setup docs
- **Issues**: Create GitHub issues in templates repository
- **Team Support**: Contact DevOps team
- **Emergency**: Use existing GitLab CI as fallback

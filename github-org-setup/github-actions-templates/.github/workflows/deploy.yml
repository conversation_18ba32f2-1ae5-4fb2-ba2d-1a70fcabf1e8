name: Deploy to Kubernetes
description: "Deploy application to Google Kubernetes Engine"

on:
  workflow_call:
    inputs:
      service_name:
        required: true
        type: string
        description: "Service name"
      namespace:
        required: true
        type: string
        description: "Kubernetes namespace"
      project_id:
        required: true
        type: string
        description: "Google Cloud Project ID"
      region:
        required: true
        type: string
        description: "Google Cloud Region"
      cluster_name:
        required: true
        type: string
        description: "GKE Cluster Name"
      image_name:
        required: true
        type: string
        description: "Full Docker image name with tag"
      
      # Resource configurations
      memory_request:
        required: false
        type: string
        default: "200Mi"
      cpu_request:
        required: false
        type: string
        default: "100m"
      memory_limit:
        required: false
        type: string
        default: "600Mi"
      cpu_limit:
        required: false
        type: string
        default: "250m"
      container_port:
        required: false
        type: string
        default: "8080"
      service_port:
        required: false
        type: string
        default: "80"
      ingress_host:
        required: false
        type: string
        description: "Ingress hostname"
    
    secrets:
      service_account_key:
        required: true
        description: "Google Cloud Service Account Key"

jobs:
  deploy:
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          service_account_key: ${{ secrets.service_account_key }}
          project_id: ${{ inputs.project_id }}

      - name: Get GKE credentials
        run: |
          echo "🔐 Getting GKE cluster credentials..."
          gcloud container clusters get-credentials ${{ inputs.cluster_name }} \
            --region ${{ inputs.region }} \
            --project ${{ inputs.project_id }}

      - name: Create namespace if not exists
        run: |
          echo "📦 Creating namespace: ${{ inputs.namespace }}"
          kubectl create namespace ${{ inputs.namespace }} --dry-run=client -o yaml | kubectl apply -f -

      - name: Generate Kubernetes manifest
        run: |
          echo "📝 Generating Kubernetes manifest..."
          
          # Copy template and substitute variables
          cp templates/k8s-manifest-template.yml k8s-manifest-${{ github.ref_name }}.yml
          
          # Parse image components
          GAR_HOSTNAME=$(echo "${{ inputs.image_name }}" | cut -d'/' -f1)
          PROJECT_ID=$(echo "${{ inputs.image_name }}" | cut -d'/' -f2)
          REPOSITORY=$(echo "${{ inputs.image_name }}" | cut -d'/' -f3)
          IMAGE_WITH_TAG=$(echo "${{ inputs.image_name }}" | cut -d'/' -f4)
          IMAGE_NAME=$(echo "${IMAGE_WITH_TAG}" | cut -d':' -f1)
          VERSION=$(echo "${IMAGE_WITH_TAG}" | cut -d':' -f2)
          ENV=$(echo "${VERSION}" | cut -d'-' -f1)
          
          # Substitute all variables in the manifest
          sed -i \
            -e "s|<SERVICE_NAME>|${{ inputs.service_name }}|g" \
            -e "s|<NAMESPACE>|${{ inputs.namespace }}|g" \
            -e "s|<GAR_HOSTNAME>|${GAR_HOSTNAME}|g" \
            -e "s|<PROJECT_ID>|${PROJECT_ID}|g" \
            -e "s|<REPOSITORY>|${REPOSITORY}|g" \
            -e "s|<IMAGE_NAME>|${IMAGE_NAME}|g" \
            -e "s|<ENV>|${ENV}|g" \
            -e "s|<VERSION>|${VERSION}|g" \
            -e "s|<MEMORY_REQUEST>|${{ inputs.memory_request }}|g" \
            -e "s|<CPU_REQUEST>|${{ inputs.cpu_request }}|g" \
            -e "s|<MEMORY_LIMIT>|${{ inputs.memory_limit }}|g" \
            -e "s|<CPU_LIMIT>|${{ inputs.cpu_limit }}|g" \
            -e "s|<CONTAINER_PORT>|${{ inputs.container_port }}|g" \
            -e "s|<SERVICE_PORT>|${{ inputs.service_port }}|g" \
            -e "s|<INGRESS_HOST>|${{ inputs.ingress_host }}|g" \
            "k8s-manifest-${{ github.ref_name }}.yml"

      - name: Check deployment status
        id: deployment-check
        run: |
          if kubectl get deployment ${{ inputs.service_name }}-dp -n ${{ inputs.namespace }} >/dev/null 2>&1; then
            echo "deployment_exists=true" >> $GITHUB_OUTPUT
            echo "📋 Existing deployment found"
          else
            echo "deployment_exists=false" >> $GITHUB_OUTPUT
            echo "🆕 No existing deployment found"
          fi

      - name: Deploy to Kubernetes
        run: |
          if [ "${{ steps.deployment-check.outputs.deployment_exists }}" = "true" ]; then
            echo "🔄 Performing rolling update..."
            kubectl set image deployment/${{ inputs.service_name }}-dp \
              ${{ inputs.service_name }}=${{ inputs.image_name }} \
              -n ${{ inputs.namespace }}
          else
            echo "🚀 Creating new deployment..."
            kubectl apply -f k8s-manifest-${{ github.ref_name }}.yml
          fi

      - name: Wait for deployment rollout
        run: |
          echo "⏳ Waiting for deployment to complete..."
          kubectl rollout status deployment/${{ inputs.service_name }}-dp \
            -n ${{ inputs.namespace }} \
            --timeout=300s

      - name: Verify deployment
        run: |
          echo "✅ Verifying deployment..."
          kubectl get pods -n ${{ inputs.namespace }} -l app=${{ inputs.service_name }}
          kubectl get services -n ${{ inputs.namespace }} -l app=${{ inputs.service_name }}
          
          # Check if ingress was created
          if kubectl get ingress ${{ inputs.service_name }}-ingress -n ${{ inputs.namespace }} >/dev/null 2>&1; then
            echo "🌐 Ingress created successfully"
            kubectl get ingress ${{ inputs.service_name }}-ingress -n ${{ inputs.namespace }}
          fi

      - name: Get service endpoints
        run: |
          echo "📍 Service endpoints:"
          kubectl get endpoints -n ${{ inputs.namespace }} -l app=${{ inputs.service_name }}
          
          if [ -n "${{ inputs.ingress_host }}" ]; then
            echo "🔗 Application URL: https://${{ inputs.ingress_host }}"
          fi

      - name: Upload manifest artifact
        uses: actions/upload-artifact@v4
        with:
          name: k8s-manifest-${{ github.ref_name }}
          path: k8s-manifest-${{ github.ref_name }}.yml
          retention-days: 30

name: Team-Aware CI/CD Pipeline
description: "Complete CI/CD pipeline with team-based configuration and Kubernetes deployment"

on:
  workflow_call:
    inputs:
      # Service configuration
      service_name:
        required: true
        type: string
        description: "Service name"
      team:
        required: true
        type: string
        description: "Team name (ruh-catalyst, ruh-aim, ruh-commons)"

      # Optional configurations with defaults
      gar_hostname:
        required: false
        type: string
        default: "us-central1-docker.pkg.dev"
        description: "Google Artifact Registry Hostname"

      # Resource configurations
      memory_request:
        required: false
        type: string
        default: "200Mi"
        description: "Memory request"
      cpu_request:
        required: false
        type: string
        default: "100m"
        description: "CPU request"
      memory_limit:
        required: false
        type: string
        default: "600Mi"
        description: "Memory limit"
      cpu_limit:
        required: false
        type: string
        default: "250m"
        description: "CPU limit"
      container_port:
        required: false
        type: string
        default: "8080"
        description: "Container port"
      service_port:
        required: false
        type: string
        default: "80"
        description: "Service port"

      # Optional custom ingress host
      ingress_host:
        required: false
        type: string
        description: "Custom ingress host (optional)"

    secrets:
      git_token:
        required: true
        description: "GitHub token for repository access"
      team_service_account_key:
        required: true
        description: "Team-specific GCP service account key"
      team_slack_webhook:
        required: true
        description: "Team-specific Slack webhook URL"

jobs:
  # Setup job - determines team configuration
  setup:
    runs-on: ubuntu-latest
    outputs:
      project_id: ${{ steps.team-config.outputs.project_id }}
      region: ${{ steps.team-config.outputs.region }}
      cluster_name: ${{ steps.team-config.outputs.cluster_name }}
      repository: ${{ steps.team-config.outputs.repository }}
      repo_url: ${{ steps.team-config.outputs.repo_url }}
      namespace: ${{ steps.team-config.outputs.namespace }}
      ingress_host: ${{ steps.team-config.outputs.ingress_host }}
      image_tag: ${{ steps.team-config.outputs.image_tag }}
      full_image_name: ${{ steps.team-config.outputs.full_image_name }}

    steps:
      - name: Determine team configuration
        id: team-config
        run: |
          # Set team-specific configuration based on input
          case "${{ inputs.team }}" in
            "ruh-catalyst")
              echo "project_id=${{ vars.RUH_CATALYST_PROJECT_ID }}" >> $GITHUB_OUTPUT
              echo "region=${{ vars.RUH_CATALYST_REGION }}" >> $GITHUB_OUTPUT
              echo "cluster_name=${{ vars.RUH_CATALYST_CLUSTER_NAME }}" >> $GITHUB_OUTPUT
              echo "repository=${{ vars.RUH_CATALYST_REPOSITORY }}" >> $GITHUB_OUTPUT
              echo "repo_url=${{ vars.RUH_CATALYST_REPO_URL }}" >> $GITHUB_OUTPUT
              namespace_prefix="ruh-catalyst"
              ;;
            "ruh-aim")
              echo "project_id=${{ vars.RUH_AIM_PROJECT_ID }}" >> $GITHUB_OUTPUT
              echo "region=${{ vars.RUH_AIM_REGION }}" >> $GITHUB_OUTPUT
              echo "cluster_name=${{ vars.RUH_AIM_CLUSTER_NAME }}" >> $GITHUB_OUTPUT
              echo "repository=${{ vars.RUH_AIM_REPOSITORY }}" >> $GITHUB_OUTPUT
              echo "repo_url=${{ vars.RUH_AIM_REPO_URL }}" >> $GITHUB_OUTPUT
              namespace_prefix="ruh-aim"
              ;;
            "ruh-commons")
              echo "project_id=${{ vars.RUH_COMMONS_PROJECT_ID }}" >> $GITHUB_OUTPUT
              echo "region=${{ vars.RUH_COMMONS_REGION }}" >> $GITHUB_OUTPUT
              echo "cluster_name=${{ vars.RUH_COMMONS_CLUSTER_NAME }}" >> $GITHUB_OUTPUT
              echo "repository=${{ vars.RUH_COMMONS_REPOSITORY }}" >> $GITHUB_OUTPUT
              echo "repo_url=${{ vars.RUH_COMMONS_REPO_URL }}" >> $GITHUB_OUTPUT
              namespace_prefix="ruh-commons"
              ;;
            *)
              echo "Error: Unknown team '${{ inputs.team }}'"
              exit 1
              ;;
          esac

          # Set dynamic values
          namespace="${namespace_prefix}-${{ github.ref_name }}"
          echo "namespace=${namespace}" >> $GITHUB_OUTPUT

          # Set ingress host (custom or default pattern)
          if [ -n "${{ inputs.ingress_host }}" ]; then
            ingress_host="${{ inputs.ingress_host }}"
          else
            ingress_host="${{ inputs.service_name }}-${namespace}.rapidinnovation.dev"
          fi
          echo "ingress_host=${ingress_host}" >> $GITHUB_OUTPUT

          # Set image tag and full image name
          image_tag="${{ github.ref_name }}-${{ github.sha }}"
          echo "image_tag=${image_tag}" >> $GITHUB_OUTPUT

          # Get the project_id and repository values that were just set
          case "${{ inputs.team }}" in
            "ruh-catalyst")
              project_id="${{ vars.RUH_CATALYST_PROJECT_ID }}"
              repository="${{ vars.RUH_CATALYST_REPOSITORY }}"
              ;;
            "ruh-aim")
              project_id="${{ vars.RUH_AIM_PROJECT_ID }}"
              repository="${{ vars.RUH_AIM_REPOSITORY }}"
              ;;
            "ruh-commons")
              project_id="${{ vars.RUH_COMMONS_PROJECT_ID }}"
              repository="${{ vars.RUH_COMMONS_REPOSITORY }}"
              ;;
          esac

          # Use team-specific project and repository
          full_image_name="${{ inputs.gar_hostname }}/${project_id}/${repository}/${{ inputs.service_name }}:${image_tag}"
          echo "full_image_name=${full_image_name}" >> $GITHUB_OUTPUT

      - name: Validate configuration
        run: |
          echo "Team: ${{ inputs.team }}"
          echo "Service: ${{ inputs.service_name }}"
          echo "Namespace: ${{ steps.team-config.outputs.namespace }}"
          echo "Image: ${{ steps.team-config.outputs.full_image_name }}"
          echo "Ingress: ${{ steps.team-config.outputs.ingress_host }}"

  # Build job
  build:
    runs-on: ubuntu-latest
    needs: setup
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          service_account_key: ${{ secrets.team_service_account_key }}
          project_id: ${{ needs.setup.outputs.project_id }}

      - name: Configure Docker for GAR
        run: |
          gcloud auth configure-docker ${{ inputs.gar_hostname }}

      - name: Build and push Docker image
        run: |
          docker build \
            --no-cache \
            --build-arg REPO_URL="${{ needs.setup.outputs.repo_url }}" \
            --build-arg GIT_TOKEN="${{ secrets.git_token }}" \
            --build-arg ENV="${{ github.ref_name }}" \
            -t "${{ needs.setup.outputs.full_image_name }}" .

          docker push "${{ needs.setup.outputs.full_image_name }}"

      - name: Notify build failure
        if: failure()
        uses: ./.github/workflows/notify.yml
        with:
          webhook_url: ${{ secrets.team_slack_webhook }}
          message: "🔴 Build failed for ${{ inputs.service_name }} on ${{ github.ref_name }}"
          pipeline_url: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}

  # Deploy job
  deploy:
    runs-on: ubuntu-latest
    needs: [setup, build]
    if: github.ref_name == 'main' || github.ref_name == 'dev' || github.ref_name == 'staging'
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Google Cloud SDK
        uses: google-github-actions/setup-gcloud@v2
        with:
          service_account_key: ${{ secrets.team_service_account_key }}
          project_id: ${{ needs.setup.outputs.project_id }}

      - name: Get GKE credentials
        run: |
          gcloud container clusters get-credentials ${{ needs.setup.outputs.cluster_name }} \
            --region ${{ needs.setup.outputs.region }} \
            --project ${{ needs.setup.outputs.project_id }}

      - name: Create namespace if not exists
        run: |
          kubectl create namespace ${{ needs.setup.outputs.namespace }} --dry-run=client -o yaml | kubectl apply -f -

      - name: Generate Kubernetes manifest
        run: |
          # Copy template and substitute variables
          cp templates/k8s-manifest-template.yml k8s-manifest-${{ github.ref_name }}.yml

          sed -i \
            -e "s|<SERVICE_NAME>|${{ inputs.service_name }}|g" \
            -e "s|<NAMESPACE>|${{ needs.setup.outputs.namespace }}|g" \
            -e "s|<GAR_HOSTNAME>|${{ inputs.gar_hostname }}|g" \
            -e "s|<PROJECT_ID>|${{ needs.setup.outputs.project_id }}|g" \
            -e "s|<REPOSITORY>|${{ needs.setup.outputs.repository }}|g" \
            -e "s|<IMAGE_NAME>|${{ inputs.service_name }}|g" \
            -e "s|<ENV>|${{ github.ref_name }}|g" \
            -e "s|<VERSION>|${{ github.sha }}|g" \
            -e "s|<MEMORY_REQUEST>|${{ inputs.memory_request }}|g" \
            -e "s|<CPU_REQUEST>|${{ inputs.cpu_request }}|g" \
            -e "s|<MEMORY_LIMIT>|${{ inputs.memory_limit }}|g" \
            -e "s|<CPU_LIMIT>|${{ inputs.cpu_limit }}|g" \
            -e "s|<CONTAINER_PORT>|${{ inputs.container_port }}|g" \
            -e "s|<SERVICE_PORT>|${{ inputs.service_port }}|g" \
            -e "s|<INGRESS_HOST>|${{ needs.setup.outputs.ingress_host }}|g" \
            "k8s-manifest-${{ github.ref_name }}.yml"

      - name: Deploy to Kubernetes
        run: |
          # Check if deployment exists
          if kubectl get deployment ${{ inputs.service_name }}-dp -n ${{ needs.setup.outputs.namespace }} >/dev/null 2>&1; then
            echo "Deployment exists, performing rolling update"
            kubectl set image deployment/${{ inputs.service_name }}-dp \
              ${{ inputs.service_name }}=${{ needs.setup.outputs.full_image_name }} \
              -n ${{ needs.setup.outputs.namespace }}
          else
            echo "Deployment not found, creating new deployment"
            kubectl apply -f k8s-manifest-${{ github.ref_name }}.yml
          fi

      - name: Wait for deployment
        run: |
          kubectl rollout status deployment/${{ inputs.service_name }}-dp -n ${{ needs.setup.outputs.namespace }} --timeout=300s

  # Final notification
  notify:
    runs-on: ubuntu-latest
    needs: [setup, build, deploy]
    if: always()
    steps:
      - name: Send final notification
        run: |
          if [ "${{ needs.deploy.result }}" = "success" ]; then
            STATUS_EMOJI="✅"
            STATUS_TEXT="succeeded"
          else
            STATUS_EMOJI="❌"
            STATUS_TEXT="failed"
          fi

          MESSAGE="${STATUS_EMOJI} Pipeline ${STATUS_TEXT} for ${{ inputs.service_name }} on ${{ github.ref_name }}! Check logs: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"

          curl -X POST -H 'Content-type: application/json' \
            --data "{\"channel\":\"ruh-cicd\",\"text\":\"${MESSAGE}\"}" \
            ${{ secrets.team_slack_webhook }}

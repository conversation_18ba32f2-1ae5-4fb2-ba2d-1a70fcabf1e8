# Example workflow for RUH Commons team - Auth Service
# This file should be placed in your service repository as .github/workflows/ci-cd.yml

name: Auth Service CI/CD

on:
  push:
    branches:
      - main
      - dev
      - staging
  workflow_dispatch:

jobs:
  ci-cd:
    name: CI/CD Pipeline
    if: github.ref_name == 'main' || github.ref_name == 'dev' || github.ref_name == 'staging'
    uses: your-org/github-actions-templates/.github/workflows/team-pipeline.yml@main
    with:
      # Service configuration
      service_name: "auth-service"
      team: "ruh-commons"

      # Standard resource configurations for shared services
      memory_request: "256Mi"
      cpu_request: "100m"
      memory_limit: "512Mi"
      cpu_limit: "300m"
      container_port: "8080"
      service_port: "80"

      # Optional: Custom ingress host for auth service
      # ingress_host: 'auth.rapidinnovation.dev'

    secrets:
      git_token: ${{ secrets.GIT_TOKEN }}
      team_service_account_key: ${{ secrets.RUH_COMMONS_SERVICE_ACCOUNT_KEY }}
      team_slack_webhook: ${{ secrets.RUH_COMMONS_SLACK_WEBHOOK }}

# This creates:
# - Namespace: ruh-commons-{branch}
# - Deployment: auth-service-dp
# - Service: auth-service-svc
# - Ingress: auth-service-ruh-commons-{branch}.rapidinnovation.dev
#
# Uses RUH_COMMONS_* variables for:
# - PROJECT_ID
# - REGION
# - CLUSTER_NAME
# - REPOSITORY
# - REPO_URL

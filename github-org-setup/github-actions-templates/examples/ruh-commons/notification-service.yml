# Example workflow for RUH Commons team - Notification Service
# This file should be placed in your service repository as .github/workflows/ci-cd.yml

name: Notification Service CI/CD

on:
  push:
    branches:
      - main
      - dev
      - staging
  workflow_dispatch:

jobs:
  ci-cd:
    name: CI/CD Pipeline
    if: github.ref_name == 'main' || github.ref_name == 'dev' || github.ref_name == 'staging'
    uses: your-org/github-actions-templates/.github/workflows/team-pipeline.yml@main
    with:
      # Service configuration
      service_name: "notification-service"
      team: "ruh-commons"

      # Resource configurations for notification service
      memory_request: "128Mi"
      cpu_request: "50m"
      memory_limit: "256Mi"
      cpu_limit: "200m"
      container_port: "8080"
      service_port: "80"

      # Optional: Custom ingress host
      # ingress_host: 'notifications.rapidinnovation.dev'

    secrets:
      git_token: ${{ secrets.GIT_TOKEN }}
      team_service_account_key: ${{ secrets.RUH_COMMONS_SERVICE_ACCOUNT_KEY }}
      team_slack_webhook: ${{ secrets.RUH_COMMONS_SLACK_WEBHOOK }}

# This creates:
# - Namespace: ruh-commons-{branch}
# - Deployment: notification-service-dp
# - Service: notification-service-svc
# - Ingress: notification-service-ruh-commons-{branch}.rapidinnovation.dev

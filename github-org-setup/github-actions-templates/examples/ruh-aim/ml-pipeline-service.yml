# Example workflow for RUH AIM team - ML Pipeline Service
# This file should be placed in your service repository as .github/workflows/ci-cd.yml

name: ML Pipeline Service CI/CD

on:
  push:
    branches:
      - main
      - dev
      - staging
  workflow_dispatch:

jobs:
  ci-cd:
    name: CI/CD Pipeline
    if: github.ref_name == 'main' || github.ref_name == 'dev' || github.ref_name == 'staging'
    uses: your-org/github-actions-templates/.github/workflows/team-pipeline.yml@main
    with:
      # Service configuration
      service_name: "ml-pipeline-service"
      team: "ruh-aim"

      # Higher resource requirements for ML workloads
      memory_request: "1Gi"
      cpu_request: "500m"
      memory_limit: "4Gi"
      cpu_limit: "2000m"
      container_port: "8080"
      service_port: "80"

      # Optional: Custom ingress host for ML pipeline
      # ingress_host: 'ml-pipeline.ai.rapidinnovation.dev'

    secrets:
      git_token: ${{ secrets.GIT_TOKEN }}
      team_service_account_key: ${{ secrets.RUH_AIM_SERVICE_ACCOUNT_KEY }}
      team_slack_webhook: ${{ secrets.RUH_AIM_SLACK_WEBHOOK }}

# This creates:
# - Namespace: ruh-aim-{branch}
# - Deployment: ml-pipeline-service-dp with higher resource limits
# - Service: ml-pipeline-service-svc
# - Ingress: ml-pipeline-service-ruh-aim-{branch}.rapidinnovation.dev

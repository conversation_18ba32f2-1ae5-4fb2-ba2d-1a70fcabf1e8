# Example workflow for RUH AIM team - AIM Service
# This file should be placed in your service repository as .github/workflows/ci-cd.yml

name: AIM Service CI/CD

on:
  push:
    branches:
      - main
      - dev
      - staging
  workflow_dispatch:

jobs:
  ci-cd:
    name: CI/CD Pipeline
    if: github.ref_name == 'main' || github.ref_name == 'dev' || github.ref_name == 'staging'
    uses: your-org/github-actions-templates/.github/workflows/team-pipeline.yml@main
    with:
      # Service configuration
      service_name: "aim-service"
      team: "ruh-aim"

      # Service-specific resource configurations for AI/ML workloads
      memory_request: "512Mi"
      cpu_request: "200m"
      memory_limit: "2Gi"
      cpu_limit: "1000m"
      container_port: "8080"
      service_port: "80"

      # Optional: Custom ingress host
      # ingress_host: 'aim-service-custom.rapidinnovation.dev'

    secrets:
      git_token: ${{ secrets.GIT_TOKEN }}
      team_service_account_key: ${{ secrets.RUH_AIM_SERVICE_ACCOUNT_KEY }}
      team_slack_webhook: ${{ secrets.RUH_AIM_SLACK_WEBHOOK }}

# This creates:
# - Namespace: ruh-aim-{branch}
# - Deployment: aim-service-dp
# - Service: aim-service-svc
# - Ingress: aim-service-ruh-aim-{branch}.rapidinnovation.dev
#
# Uses RUH_AIM_* variables for:
# - PROJECT_ID
# - REGION
# - CLUSTER_NAME
# - REPOSITORY
# - REPO_URL

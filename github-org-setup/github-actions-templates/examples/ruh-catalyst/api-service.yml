# Example workflow for RUH Catalyst team - API Service
# This file should be placed in your service repository as .github/workflows/ci-cd.yml

name: API Service CI/CD

on:
  push:
    branches:
      - main
      - dev
      - staging
  workflow_dispatch:

jobs:
  ci-cd:
    name: CI/CD Pipeline
    if: github.ref_name == 'main' || github.ref_name == 'dev' || github.ref_name == 'staging'
    uses: your-org/github-actions-templates/.github/workflows/team-pipeline.yml@main
    with:
      # Service configuration
      service_name: "api-service"
      team: "ruh-catalyst"

      # Service-specific resource configurations
      memory_request: "256Mi"
      cpu_request: "150m"
      memory_limit: "1Gi"
      cpu_limit: "500m"
      container_port: "8080" # HTTP API port
      service_port: "80"

      # Optional: Custom ingress host
      # ingress_host: 'api-service-custom.rapidinnovation.dev'

    secrets:
      git_token: ${{ secrets.GIT_TOKEN }}
      team_service_account_key: ${{ secrets.RUH_CATALYST_SERVICE_ACCOUNT_KEY }}
      team_slack_webhook: ${{ secrets.RUH_CATALYST_SLACK_WEBHOOK }}

# This creates:
# - Namespace: ruh-catalyst-{branch}
# - Deployment: api-service-dp
# - Service: api-service-svc
# - Ingress: api-service-ruh-catalyst-{branch}.rapidinnovation.dev

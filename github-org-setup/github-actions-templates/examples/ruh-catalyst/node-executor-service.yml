# Example workflow for RUH Catalyst team - Node Executor Service
# This file should be placed in your service repository as .github/workflows/ci-cd.yml

name: Node Executor Service CI/CD

on:
  push:
    branches:
      - main
      - dev
      - staging
  workflow_dispatch:

jobs:
  ci-cd:
    name: CI/CD Pipeline
    if: github.ref_name == 'main' || github.ref_name == 'dev' || github.ref_name == 'staging'
    uses: your-org/github-actions-templates/.github/workflows/team-pipeline.yml@main
    with:
      # Service configuration
      service_name: "node-executor-service"
      team: "ruh-catalyst" # This service belongs to the ruh-catalyst team

      # Service-specific resource configurations (matching your GitLab CI setup)
      memory_request: "200Mi"
      cpu_request: "100m"
      memory_limit: "600Mi"
      cpu_limit: "250m"
      container_port: "50052" # gRPC port as per your original config
      service_port: "80"

      # Optional: Custom ingress host (if not provided, will use default pattern)
      # ingress_host: 'node-executor-ruh-catalyst-${{ github.ref_name }}.rapidinnovation.dev'

    secrets:
      git_token: ${{ secrets.GIT_TOKEN }}
      
      # Team-specific secrets (ruh-catalyst team)
      team_service_account_key: ${{ secrets.RUH_CATALYST_SERVICE_ACCOUNT_KEY }}
      team_slack_webhook: ${{ secrets.RUH_CATALYST_SLACK_WEBHOOK }}

# This workflow will create the following Kubernetes resources:
# - Namespace: ruh-catalyst-dev, ruh-catalyst-staging, or ruh-catalyst-main
# - Deployment: node-executor-service-dp
# - Service: node-executor-service-svc
# - ServiceAccount: node-executor-service-sa
# - Ingress: node-executor-service-ingress
#
# The Docker image will be tagged as:
# us-central1-docker.pkg.dev/{PROJECT_ID}/{REPOSITORY}/node-executor-service:{ENVIRONMENT}-{SHORT_SHA}
#
# Example: us-central1-docker.pkg.dev/ruh-prod-123/ruh-catalyst/node-executor-service:dev-a1b2c3d4

# Example using the basic main-pipeline workflow (without team auto-detection)
# This file should be placed in your service repository as .github/workflows/ci-cd.yml

name: Service CI/CD (Basic Pipeline)

on:
  push:
    branches:
      - main
      - dev
      - staging
  workflow_dispatch:

jobs:
  ci-cd:
    name: CI/CD Pipeline
    if: github.ref_name == 'main' || github.ref_name == 'dev' || github.ref_name == 'staging'
    uses: your-org/github-actions-templates/.github/workflows/main-pipeline.yml@main
    with:
      # Service configuration
      service_name: "my-service"
      namespace_prefix: "ruh-catalyst" # This creates namespaces like ruh-catalyst-dev, ruh-catalyst-staging, etc.

      # Google Cloud configuration (using organization variables)
      project_id: ${{ vars.RUH_CATALYST_PROJECT_ID }}
      region: ${{ vars.RUH_CATALYST_REGION }}
      cluster_name: ${{ vars.RUH_CATALYST_CLUSTER_NAME }}
      repository: ${{ vars.RUH_CATALYST_REPOSITORY }}
      repo_url: ${{ vars.RUH_CATALYST_REPO_URL }}

      # Optional: Override default GAR hostname
      # gar_hostname: "us-central1-docker.pkg.dev"

      # Service-specific resource configurations
      memory_request: "200Mi"
      cpu_request: "100m"
      memory_limit: "600Mi"
      cpu_limit: "250m"
      container_port: "8080"
      service_port: "80"

      # Optional: Custom ingress host
      # ingress_host: 'my-service-custom.rapidinnovation.dev'

    secrets:
      git_token: ${{ secrets.GIT_TOKEN }}
      service_account_key: ${{ secrets.RUH_CATALYST_SERVICE_ACCOUNT_KEY }}
      slack_webhook: ${{ secrets.RUH_CATALYST_SLACK_WEBHOOK }}

# This workflow will create the following Kubernetes resources:
# - Namespace: ruh-catalyst-dev, ruh-catalyst-staging, or ruh-catalyst-main
# - Deployment: my-service-dp
# - Service: my-service-svc
# - ServiceAccount: my-service-sa
# - Ingress: my-service-ingress
#
# The Docker image will be tagged as:
# us-central1-docker.pkg.dev/{PROJECT_ID}/{REPOSITORY}/my-service:{ENVIRONMENT}-{SHA}
#
# Example: us-central1-docker.pkg.dev/ruh-prod-123/ruh-catalyst/my-service:dev-a1b2c3d4

# Benefits of basic pipeline:
# - More explicit configuration
# - Easier to understand variable usage
# - Can mix and match team configurations
# - Good for services that don't fit standard team patterns

# When to use team-pipeline.yml instead:
# - Standard team-based services
# - Want automatic team variable detection
# - Prefer convention over configuration
# - Consistent team-based deployments

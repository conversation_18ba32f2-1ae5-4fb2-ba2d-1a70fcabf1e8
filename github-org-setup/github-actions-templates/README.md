# GitHub Actions Templates for RUH Organization

This repository contains reusable GitHub Actions workflows and templates that mirror the GitLab CI setup, providing a centralized CI/CD solution for all RUH teams.

## 🏗️ Structure

```
github-actions-templates/
├── .github/
│   └── workflows/                    # Reusable workflows (equivalent to GitLab templates)
│       ├── setup.yml                # Authentication and setup
│       ├── build.yml                # Docker build and push
│       ├── deploy.yml               # Kubernetes deployment
│       ├── notify.yml               # Slack notifications
│       └── team-pipeline.yml        # Complete team-aware pipeline
├── templates/
│   ├── k8s-manifest-template.yml    # Kubernetes manifest template
│   └── ingress-template.yml         # Ingress template
├── examples/                         # Example workflows for each team
│   ├── ruh-catalyst/
│   ├── ruh-aim/
│   └── ruh-commons/
└── docs/                            # Documentation
    ├── setup-guide.md
    ├── team-configuration.md
    └── migration-guide.md
```

## 🚀 Quick Start

### 1. Set Up GitHub Teams

Create teams in your GitHub organization:
- `@your-org/ruh-catalyst`
- `@your-org/ruh-aim`
- `@your-org/ruh-commons`

### 2. Configure Organization Variables

Set up team-specific variables in your GitHub organization settings:

#### Global Variables (Organization Level):
```
GAR_HOSTNAME=us-central1-docker.pkg.dev
```

#### Team-Specific Variables:

**For RUH Catalyst Team:**
```
RUH_CATALYST_PROJECT_ID=your-gcp-project-id
RUH_CATALYST_REGION=us-central1
RUH_CATALYST_CLUSTER_NAME=ruh-catalyst-cluster
RUH_CATALYST_REPOSITORY=ruh-catalyst
RUH_CATALYST_REPO_URL=https://github.com/your-org
```

**For RUH AIM Team:**
```
RUH_AIM_PROJECT_ID=your-gcp-project-id
RUH_AIM_REGION=us-central1
RUH_AIM_CLUSTER_NAME=ruh-aim-cluster
RUH_AIM_REPOSITORY=ruh-aim
RUH_AIM_REPO_URL=https://github.com/your-org
```

**For RUH Commons Team:**
```
RUH_COMMONS_PROJECT_ID=your-gcp-project-id
RUH_COMMONS_REGION=us-central1
RUH_COMMONS_CLUSTER_NAME=ruh-commons-cluster
RUH_COMMONS_REPOSITORY=ruh-commons
RUH_COMMONS_REPO_URL=https://github.com/your-org
```

### 3. Configure Team Secrets

Set up team-specific secrets:

**For each team:**
```
RUH_CATALYST_SERVICE_ACCOUNT_KEY=<base64-encoded-service-account-key>
RUH_CATALYST_SLACK_WEBHOOK=<slack-webhook-url>

RUH_AIM_SERVICE_ACCOUNT_KEY=<base64-encoded-service-account-key>
RUH_AIM_SLACK_WEBHOOK=<slack-webhook-url>

RUH_COMMONS_SERVICE_ACCOUNT_KEY=<base64-encoded-service-account-key>
RUH_COMMONS_SLACK_WEBHOOK=<slack-webhook-url>
```

**Global Secrets:**
```
GIT_TOKEN=<github-personal-access-token>
```

## 📝 Usage

### Basic Usage in Your Service Repository

Create `.github/workflows/ci-cd.yml` in your service repository:

```yaml
name: Service CI/CD

on:
  push:
    branches: [main, dev, staging]
  workflow_dispatch:

jobs:
  ci-cd:
    uses: your-org/github-actions-templates/.github/workflows/team-pipeline.yml@main
    with:
      service_name: 'your-service-name'
      team: 'ruh-catalyst'  # or ruh-aim, ruh-commons
      memory_request: '200Mi'
      cpu_request: '100m'
      memory_limit: '600Mi'
      cpu_limit: '250m'
      container_port: '8080'
      service_port: '80'
    secrets:
      git_token: ${{ secrets.GIT_TOKEN }}
      team_service_account_key: ${{ secrets.RUH_CATALYST_SERVICE_ACCOUNT_KEY }}
      team_slack_webhook: ${{ secrets.RUH_CATALYST_SLACK_WEBHOOK }}
```

## 🔄 Migration from GitLab CI

This setup provides equivalent functionality to your GitLab CI templates:

| GitLab CI | GitHub Actions | Description |
|-----------|----------------|-------------|
| `.setup_template` | `setup.yml` | Authentication and setup |
| `.build_template` | `build.yml` | Docker build and push |
| `.deploy_template` | `deploy.yml` | Kubernetes deployment |
| `.slack_notification_*` | `notify.yml` | Slack notifications |
| Group variables | Organization variables | Team-specific configuration |
| Group namespaces | Team-based namespaces | `ruh-{team}-{branch}` |

## 🎯 Features

- **Team-Aware**: Automatically detects team and uses team-specific variables
- **Dynamic Namespaces**: Creates namespaces like `ruh-catalyst-dev`, `ruh-aim-staging`
- **Resource Management**: Configurable CPU/memory limits per service
- **Slack Integration**: Team-specific notifications
- **Multi-Environment**: Supports dev, staging, main branches
- **Reusable**: DRY principle with centralized templates

## 🔧 Advanced Configuration

See the `docs/` folder for detailed configuration guides and examples.

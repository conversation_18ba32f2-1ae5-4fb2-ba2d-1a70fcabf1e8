#!/bin/bash

# Script to set up the github-actions-templates repository
# Run this after creating the PRIVATE repository on GitHub

set -e

# Configuration - UPDATE THESE
ORG_NAME="your-org-name"  # Replace with your actual GitHub organization name
REPO_URL="https://github.com/${ORG_NAME}/github-actions-templates.git"

echo "🚀 Setting up GitHub Actions Templates Repository"
echo "Organization: ${ORG_NAME}"
echo "Repository URL: ${REPO_URL}"
echo

# Check if templates directory exists
if [ ! -d "github-actions-templates" ]; then
    echo "❌ Error: github-actions-templates directory not found"
    echo "Please run this script from the github-org-setup directory"
    exit 1
fi

# Clone the empty repository
echo "📥 Cloning the empty repository..."
git clone ${REPO_URL} temp-templates-repo
cd temp-templates-repo

# Copy all template files
echo "📁 Copying template files..."
cp -r ../github-actions-templates/* .

# Add and commit files
echo "📝 Committing files..."
git add .
git commit -m "Initial commit: Add GitHub Actions templates

- Add reusable workflows (setup, build, deploy, notify)
- Add team-aware pipeline workflow
- Add Kubernetes manifest templates
- Add examples for all teams
- Add comprehensive documentation
- Add setup scripts"

# Push to repository
echo "📤 Pushing to GitHub..."
git push origin main

# Clean up
cd ..
rm -rf temp-templates-repo

echo "✅ Templates repository setup complete!"
echo
echo "⚠️  IMPORTANT: Make sure the repository is PRIVATE for security"
echo
echo "Next steps:"
echo "1. Set repository to PRIVATE in GitHub settings"
echo "2. Add team access: ruh-catalyst, ruh-aim, ruh-commons (Read access)"
echo "3. Set up organization variables and secrets"
echo "4. Update the node-executor-service workflow file"
echo "5. Test the pipeline"
echo
echo "Repository URL: ${REPO_URL}"

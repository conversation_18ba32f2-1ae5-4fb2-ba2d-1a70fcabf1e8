name: RUH Catalyst Service CI/CD

on:
  push:
    branches:
      - main
      - dev
      - staging
  workflow_dispatch:

jobs:
  ci-cd:
    name: CI/CD Pipeline
    if: github.ref_name == 'main' || github.ref_name == 'dev' || github.ref_name == 'staging'
    uses: your-org/github-actions-templates/.github/workflows/team-pipeline.yml@main
    with:
      # Service configuration
      service_name: "node-executor-service" # Change this for each service
      team: "ruh-catalyst"

      # Optional: Override default resource configurations for this service
      memory_request: "200Mi"
      cpu_request: "100m"
      memory_limit: "600Mi"
      cpu_limit: "250m"
      container_port: "50052"
      service_port: "80"

      # Optional: Custom ingress host (if not provided, will use default pattern)
      # ingress_host: 'node-executor-custom-domain.com'

    secrets:
      # Global secrets
      git_token: ${{ secrets.GIT_TOKEN }}

      # Team-specific secrets
      team_service_account_key: ${{ secrets.RUH_CATALYST_SERVICE_ACCOUNT_KEY }}
      team_slack_webhook: ${{ secrets.RUH_CATALYST_SLACK_WEBHOOK }}

name: RUH Commons Service CI/CD

on:
  push:
    branches:
      - main
      - dev
      - staging
  workflow_dispatch:

jobs:
  ci-cd:
    name: CI/CD Pipeline
    if: github.ref_name == 'main' || github.ref_name == 'dev' || github.ref_name == 'staging'
    uses: your-org/github-actions-templates/.github/workflows/team-pipeline.yml@main
    with:
      # Service configuration
      service_name: "commons-service" # Change this for each service
      team: "ruh-commons"

      # Optional: Override default resource configurations for this service
      memory_request: "128Mi"
      cpu_request: "100m"
      memory_limit: "512Mi"
      cpu_limit: "200m"
      container_port: "8080"
      service_port: "80"

      # Optional: Custom ingress host (if not provided, will use default pattern)
      # ingress_host: 'commons-service-custom-domain.com'

    secrets:
      # Global secrets
      git_token: ${{ secrets.GIT_TOKEN }}

      # Team-specific secrets
      team_service_account_key: ${{ secrets.RUH_COMMONS_SERVICE_ACCOUNT_KEY }}
      team_slack_webhook: ${{ secrets.RUH_COMMONS_SLACK_WEBHOOK }}

name: RUH AIM Service CI/CD

on:
  push:
    branches:
      - main
      - dev
      - staging
  workflow_dispatch:

jobs:
  ci-cd:
    name: CI/CD Pipeline
    if: github.ref_name == 'main' || github.ref_name == 'dev' || github.ref_name == 'staging'
    uses: your-org/github-actions-templates/.github/workflows/team-pipeline.yml@main
    with:
      # Service configuration
      service_name: "aim-service" # Change this for each service
      team: "ruh-aim"

      # Optional: Override default resource configurations for this service
      memory_request: "256Mi"
      cpu_request: "150m"
      memory_limit: "1Gi"
      cpu_limit: "500m"
      container_port: "8080"
      service_port: "80"

      # Optional: Custom ingress host (if not provided, will use default pattern)
      # ingress_host: 'aim-service-custom-domain.com'

    secrets:
      # Global secrets
      git_token: ${{ secrets.GIT_TOKEN }}

      # Team-specific secrets
      team_service_account_key: ${{ secrets.RUH_AIM_SERVICE_ACCOUNT_KEY }}
      team_slack_webhook: ${{ secrets.RUH_AIM_SLACK_WEBHOOK }}

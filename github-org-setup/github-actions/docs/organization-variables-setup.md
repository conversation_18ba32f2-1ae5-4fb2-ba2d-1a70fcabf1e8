# GitHub Organization Variables Setup

This document explains how to set up organization-level variables in GitHub to replicate the GitLab group-level variables functionality.

## Overview

In GitLab, you had group-level variables that were available to all projects within a group. In GitHub, we achieve similar functionality using:

1. **Organization Variables** - Available to all repositories in the organization
2. **Organization Secrets** - Secure variables available to all repositories
3. **Environment Variables** - Environment-specific variables

## Required Organization Variables

### 1. Organization Variables (Public)

Navigate to your GitHub organization → Settings → Variables and actions → Variables

Create the following variables:

| Variable Name | Description | Example Value |
|---------------|-------------|---------------|
| `CLUSTER_NAME` | GKE Cluster Name | `ruh-production-cluster` |
| `PROJECT_ID` | Google Cloud Project ID | `ruh-production-12345` |
| `REGION` | Google Cloud Region | `us-central1` |
| `REPOSITORY` | Artifact Registry Repository | `ruh` |
| `REPO_URL` | Base repository URL | `https://github.com/your-org` |

### 2. Organization Secrets (Secure)

Navigate to your GitHub organization → Settings → Variables and actions → Secrets

Create the following secrets:

| Secret Name | Description | How to Get |
|-------------|-------------|------------|
| `SERVICE_ACCOUNT_KEY` | Google Cloud Service Account JSON Key | Download from GCP Console → IAM & Admin → Service Accounts |
| `GIT_TOKEN` | GitHub Personal Access Token | GitHub Settings → Developer settings → Personal access tokens |
| `SLACK_WEBHOOK_SECRET` | Slack Webhook URL | Slack App → Incoming Webhooks |

## Group/Namespace Implementation

### Namespace Prefix Configuration

In your GitLab setup, you used groups like `ruh-catalyst`, `ruh-aim`, `ruh-commons`. In GitHub Actions, we implement this through the `namespace_prefix` parameter:

```yaml
# For ruh-catalyst group services
namespace_prefix: 'ruh-catalyst'

# For ruh-aim group services  
namespace_prefix: 'ruh-aim'

# For ruh-commons group services
namespace_prefix: 'ruh-commons'
```

### Dynamic Namespace Creation

The system automatically creates Kubernetes namespaces following this pattern:
```
{namespace_prefix}-{branch_name}
```

Examples:
- `ruh-catalyst-dev` (for dev branch)
- `ruh-catalyst-staging` (for staging branch)
- `ruh-catalyst-main` (for main branch)
- `ruh-aim-dev` (for dev branch in ruh-aim group)

## Setting Up Organization Variables

### Step 1: Access Organization Settings

1. Go to your GitHub organization
2. Click on "Settings" tab
3. In the left sidebar, click "Variables and actions"
4. Click on "Variables" or "Secrets" tab

### Step 2: Create Variables

For each variable listed above:

1. Click "New organization variable" or "New organization secret"
2. Enter the variable name (exactly as shown in tables above)
3. Enter the value
4. Select repository access:
   - **Selected repositories**: Choose specific repositories
   - **All repositories**: Available to all repos in the organization
5. Click "Add variable" or "Add secret"

### Step 3: Verify Access

Ensure your repositories have access to these variables by checking:
1. Repository Settings → Variables and actions → Variables/Secrets
2. You should see organization variables/secrets listed

## Environment-Specific Configuration

### Branch-Based Environments

The system automatically maps Git branches to environments:

| Git Branch | Environment | Kubernetes Namespace |
|------------|-------------|---------------------|
| `main` | production | `{prefix}-main` |
| `staging` | staging | `{prefix}-staging` |
| `dev` | development | `{prefix}-dev` |

### Custom Environment Variables

You can override default values per service by setting them in the workflow file:

```yaml
# In your service's workflow file
with:
  memory_request: '200Mi'
  cpu_request: '100m'
  memory_limit: '600Mi'
  cpu_limit: '250m'
  container_port: '50052'
  service_port: '80'
```

## Service Account Permissions

Your Google Cloud Service Account needs the following permissions:

### Required IAM Roles

1. **Artifact Registry Writer** - To push Docker images
2. **Kubernetes Engine Developer** - To deploy to GKE
3. **Service Account User** - To use service accounts
4. **Cloud Build Editor** - For build operations (if using Cloud Build)

### Required APIs

Enable these APIs in your Google Cloud Project:

```bash
gcloud services enable container.googleapis.com
gcloud services enable artifactregistry.googleapis.com
gcloud services enable cloudbuild.googleapis.com
```

## Security Best Practices

### 1. Principle of Least Privilege

- Only grant necessary permissions to service accounts
- Use separate service accounts for different environments if needed
- Regularly rotate service account keys

### 2. Secret Management

- Never commit secrets to code
- Use GitHub organization secrets for sensitive data
- Consider using external secret management (e.g., HashiCorp Vault)

### 3. Access Control

- Limit organization variable access to specific repositories when possible
- Use environment protection rules for production deployments
- Enable branch protection rules

## Troubleshooting

### Common Issues

1. **Variables not available in workflow**
   - Check repository access to organization variables
   - Verify variable names match exactly (case-sensitive)

2. **Authentication failures**
   - Verify service account key is valid JSON
   - Check service account permissions
   - Ensure required APIs are enabled

3. **Deployment failures**
   - Check Kubernetes cluster connectivity
   - Verify namespace creation permissions
   - Check resource quotas

### Debug Commands

Add these steps to your workflow for debugging:

```yaml
- name: Debug Variables
  run: |
    echo "Project ID: ${{ vars.PROJECT_ID }}"
    echo "Region: ${{ vars.REGION }}"
    echo "Cluster: ${{ vars.CLUSTER_NAME }}"
    echo "Repository: ${{ vars.REPOSITORY }}"
    echo "Branch: ${{ github.ref_name }}"
```

## Migration from GitLab

### Variable Mapping

| GitLab Group Variable | GitHub Organization Variable |
|----------------------|------------------------------|
| `CLUSTER_NAME` | `CLUSTER_NAME` |
| `GIT_TOKEN` | `GIT_TOKEN` (secret) |
| `PROJECT_ID` | `PROJECT_ID` |
| `REGION` | `REGION` |
| `REPOSITORY` | `REPOSITORY` |
| `REPO_URL` | `REPO_URL` |
| `SERVICE_ACCOUNT_KEY` | `SERVICE_ACCOUNT_KEY` (secret) |

### Workflow Migration

1. Copy your existing `.gitlab-ci.yml` logic
2. Replace with appropriate GitHub Actions workflow
3. Update variable references from `$VARIABLE` to `${{ vars.VARIABLE }}`
4. Update secret references to `${{ secrets.SECRET_NAME }}`

## Next Steps

1. Set up organization variables as described above
2. Copy the appropriate workflow file to your service repository
3. Customize the workflow for your specific service needs
4. Test the pipeline with a sample deployment
5. Monitor and adjust resource limits as needed
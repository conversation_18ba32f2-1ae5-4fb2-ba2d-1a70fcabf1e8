# GitHub Teams Setup (GitLab Groups Equivalent)

This document explains how to implement GitHub Teams to replicate GitLab groups functionality, providing team-specific variables, access control, and namespace management.

## Overview

GitHub Teams provide similar functionality to GitLab groups:

- **Team-specific variables and secrets**
- **Repository access control**
- **Team-based permissions**
- **Namespace management per team**

## Team Structure

Based on your GitLab groups, create the following GitHub Teams:

### 1. Core Teams (equivalent to GitLab groups)

| GitLab Group | GitHub Team | Description |
|--------------|-------------|-------------|
| `ruh-catalyst` | `@your-org/ruh-catalyst` | Catalyst team services |
| `ruh-aim` | `@your-org/ruh-aim` | AIM team services |
| `ruh-commons` | `@your-org/ruh-commons` | Commons/shared services |

### 2. Additional Teams (as needed)

| GitHub Team | Purpose |
|-------------|---------|
| `@your-org/devops` | DevOps and infrastructure |
| `@your-org/platform` | Platform services |
| `@your-org/data` | Data services |

## Setting Up GitHub Teams

### Step 1: Create Teams

1. Go to your GitHub organization
2. Click "Teams" tab
3. Click "New team"
4. Configure each team:

```
Team name: ruh-catalyst
Description: RUH Catalyst team services and repositories
Visibility: Visible (or Secret if needed)
```

### Step 2: Add Team Members

1. Go to team page
2. Click "Members" tab
3. Add team members with appropriate roles:
   - **Maintainer**: Full team management
   - **Member**: Standard team member

### Step 3: Configure Team Repository Access

1. Go to team page
2. Click "Repositories" tab
3. Add repositories with appropriate permissions:
   - **Admin**: Full repository access
   - **Write**: Push access
   - **Read**: Read-only access

## Team-Specific Variables and Secrets

### Environment-Based Variables

Create environment-specific variables for each team:

#### For `ruh-catalyst` team:

**Variables:**
- `RUH_CATALYST_CLUSTER_NAME`
- `RUH_CATALYST_PROJECT_ID`
- `RUH_CATALYST_REGION`
- `RUH_CATALYST_REPOSITORY`
- `RUH_CATALYST_NAMESPACE_PREFIX` = `ruh-catalyst`

**Secrets:**
- `RUH_CATALYST_SERVICE_ACCOUNT_KEY`
- `RUH_CATALYST_SLACK_WEBHOOK`

#### For `ruh-aim` team:

**Variables:**
- `RUH_AIM_CLUSTER_NAME`
- `RUH_AIM_PROJECT_ID`
- `RUH_AIM_REGION`
- `RUH_AIM_REPOSITORY`
- `RUH_AIM_NAMESPACE_PREFIX` = `ruh-aim`

**Secrets:**
- `RUH_AIM_SERVICE_ACCOUNT_KEY`
- `RUH_AIM_SLACK_WEBHOOK`

#### For `ruh-commons` team:

**Variables:**
- `RUH_COMMONS_CLUSTER_NAME`
- `RUH_COMMONS_PROJECT_ID`
- `RUH_COMMONS_REGION`
- `RUH_COMMONS_REPOSITORY`
- `RUH_COMMONS_NAMESPACE_PREFIX` = `ruh-commons`

**Secrets:**
- `RUH_COMMONS_SERVICE_ACCOUNT_KEY`
- `RUH_COMMONS_SLACK_WEBHOOK`

## Team-Aware Workflow Templates

Let me create team-specific workflow templates that automatically detect the team and use appropriate variables.
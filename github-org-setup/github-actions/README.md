# GitHub Actions CI/CD Templates

This repository contains reusable GitHub Actions workflows that replicate GitLab CI functionality with team-based organization structure.

## 🏗️ Architecture Overview

```
github-actions/
├── workflows/                    # Reusable workflows (equivalent to GitLab templates)
│   ├── setup.yml                # Authentication setup
│   ├── build.yml                # Docker build and push
│   ├── deploy.yml               # Kubernetes deployment
│   ├── notify.yml               # Slack notifications
│   ├── main-pipeline.yml        # Basic pipeline orchestration
│   └── team-pipeline.yml        # Team-aware pipeline (recommended)
├── templates/
│   └── k8s-manifest-template.yml # Kubernetes manifest template
├── examples/                     # Example workflows for each team
│   ├── ruh-catalyst-service-workflow.yml
│   ├── ruh-aim-service-workflow.yml
│   └── ruh-commons-service-workflow.yml
└── docs/                        # Documentation
    ├── team-based-setup.md      # Team setup guide
    ├── organization-variables-setup.md
    └── migration-guide.md
```

## 🚀 Quick Start

### 1. Set Up GitHub Teams

Create teams in your GitHub organization:
- `@your-org/ruh-catalyst`
- `@your-org/ruh-aim`
- `@your-org/ruh-commons`

### 2. Configure Team Variables

Set up team-specific variables in your organization:

#### For RUH Catalyst Team:
```
RUH_CATALYST_PROJECT_ID=your-gcp-project-id
RUH_CATALYST_REGION=us-central1
RUH_CATALYST_CLUSTER_NAME=ruh-catalyst-cluster
RUH_CATALYST_REPOSITORY=ruh-catalyst
RUH_CATALYST_REPO_URL=https://github.com/your-org
```

#### For RUH AIM Team:
```
RUH_AIM_PROJECT_ID=your-gcp-project-id
RUH_AIM_REGION=us-central1
RUH_AIM_CLUSTER_NAME=ruh-aim-cluster
RUH_AIM_REPOSITORY=ruh-aim
RUH_AIM_REPO_URL=https://github.com/your-org
```

#### For RUH Commons Team:
```
RUH_COMMONS_PROJECT_ID=your-gcp-project-id
RUH_COMMONS_REGION=us-central1
RUH_COMMONS_CLUSTER_NAME=ruh-commons-cluster
RUH_COMMONS_REPOSITORY=ruh-commons
RUH_COMMONS_REPO_URL=https://github.com/your-org
```

### 3. Configure Team Secrets

Set up team-specific secrets:
- `RUH_CATALYST_SERVICE_ACCOUNT_KEY`
- `RUH_CATALYST_SLACK_WEBHOOK`
- `RUH_AIM_SERVICE_ACCOUNT_KEY`
- `RUH_AIM_SLACK_WEBHOOK`
- `RUH_COMMONS_SERVICE_ACCOUNT_KEY`
- `RUH_COMMONS_SLACK_WEBHOOK`
- `GIT_TOKEN` (global)

### 4. Use in Your Service Repository

Copy the appropriate example workflow to your service repository as `.github/workflows/ci-cd.yml`:

```yaml
name: My Service CI/CD

on:
  push:
    branches: [main, dev, staging]
  workflow_dispatch:

jobs:
  ci-cd:
    uses: your-org/github-actions-templates/.github/workflows/team-pipeline.yml@main
    with:
      service_name: 'my-service'
      team: 'ruh-catalyst'  # or ruh-aim, ruh-commons
      memory_request: '200Mi'
      cpu_request: '100m'
      memory_limit: '600Mi'
      cpu_limit: '250m'
      container_port: '8080'
      service_port: '80'
    secrets:
      git_token: ${{ secrets.GIT_TOKEN }}
      team_service_account_key: ${{ secrets.RUH_CATALYST_SERVICE_ACCOUNT_KEY }}
      team_slack_webhook: ${{ secrets.RUH_CATALYST_SLACK_WEBHOOK }}
```

## 🔧 Features

### ✅ Team-Based Organization
- **Automatic team detection** and configuration
- **Team-specific variables** and secrets
- **Isolated namespaces** per team and environment

### ✅ Environment Management
- **Branch-based environments**: `main`, `dev`, `staging`
- **Dynamic namespace creation**: `{team}-{environment}`
- **Environment-specific configurations**

### ✅ Kubernetes Deployment
- **Automated namespace management**
- **Rolling updates** for existing deployments
- **Resource limit configuration**
- **Ingress setup** with custom domains

### ✅ Notifications
- **Slack integration** with team-specific channels
- **Build failure notifications**
- **Deployment status updates**
- **Final pipeline status**

### ✅ Security
- **Team-isolated secrets**
- **Service account per team**
- **Least privilege access**

## 🌍 Namespace Structure

The system creates Kubernetes namespaces following this pattern:

| Team | Environment | Namespace |
|------|-------------|-----------|
| ruh-catalyst | dev | `ruh-catalyst-dev` |
| ruh-catalyst | staging | `ruh-catalyst-staging` |
| ruh-catalyst | main | `ruh-catalyst-main` |
| ruh-aim | dev | `ruh-aim-dev` |
| ruh-aim | staging | `ruh-aim-staging` |
| ruh-aim | main | `ruh-aim-main` |
| ruh-commons | dev | `ruh-commons-dev` |
| ruh-commons | staging | `ruh-commons-staging` |
| ruh-commons | main | `ruh-commons-main` |

## 🔄 Migration from GitLab CI

### GitLab vs GitHub Actions Mapping

| GitLab CI | GitHub Actions | Description |
|-----------|----------------|-------------|
| `extends: .template` | `uses: org/repo/.github/workflows/workflow.yml` | Reusable workflows |
| Group variables | Organization variables | `${{ vars.VARIABLE }}` |
| Group secrets | Organization secrets | `${{ secrets.SECRET }}` |
| `$CI_COMMIT_REF_NAME` | `${{ github.ref_name }}` | Branch name |
| `$CI_COMMIT_SHORT_SHA` | `$(echo ${{ github.sha }} \| cut -c1-8)` | Short commit SHA |
| `$CI_PROJECT_NAME` | `${{ github.event.repository.name }}` | Repository name |

### Migration Steps

1. **Set up GitHub teams** (equivalent to GitLab groups)
2. **Configure team variables** and secrets
3. **Copy Kubernetes manifest template** to service repositories
4. **Replace `.gitlab-ci.yml`** with GitHub Actions workflow
5. **Test deployment** on dev branch first

## 📚 Documentation

- [Team-Based Setup Guide](docs/team-based-setup.md)
- [Organization Variables Setup](docs/organization-variables-setup.md)
- [Migration Guide](docs/migration-guide.md)

## 🛠️ Customization

### Resource Limits
```yaml
with:
  memory_request: '512Mi'
  cpu_request: '200m'
  memory_limit: '1Gi'
  cpu_limit: '500m'
```

### Custom Ingress
```yaml
with:
  ingress_host: 'my-service-${{ github.ref_name }}.custom-domain.com'
```

### Environment-Specific Configuration
```yaml
with:
  memory_request: ${{ github.ref_name == 'main' && '1Gi' || '512Mi' }}
```

## 🐛 Troubleshooting

### Common Issues

1. **Team variables not found**
   - Verify team name matches exactly: `ruh-catalyst`, `ruh-aim`, `ruh-commons`
   - Check organization variables are set with correct prefixes

2. **Authentication failures**
   - Verify service account key is valid JSON
   - Check service account has required permissions
   - Ensure required GCP APIs are enabled

3. **Deployment failures**
   - Check Kubernetes cluster connectivity
   - Verify namespace creation permissions
   - Validate resource quotas

### Debug Mode

Add this step to your workflow for debugging:
```yaml
- name: Debug Team Configuration
  run: |
    echo "Team: ${{ inputs.team }}"
    echo "Service: ${{ inputs.service_name }}"
    echo "Environment: ${{ github.ref_name }}"
    echo "Namespace: ${{ inputs.team }}-${{ github.ref_name }}"
```

## 🤝 Contributing

1. Fork this repository
2. Create a feature branch
3. Make your changes
4. Test with a sample service
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- Create an issue in this repository
- Contact the DevOps team
- Check the documentation in the `docs/` folder
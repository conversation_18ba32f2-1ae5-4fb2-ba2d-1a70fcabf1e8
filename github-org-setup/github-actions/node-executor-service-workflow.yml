name: Node Executor Service CI/CD

on:
  push:
    branches:
      - main
      - dev
      - staging
  workflow_dispatch:

jobs:
  ci-cd:
    name: CI/CD Pipeline
    if: github.ref_name == 'main' || github.ref_name == 'dev' || github.ref_name == 'staging'
    uses: ./.github/workflows/main-pipeline.yml
    with:
      # Service configuration
      service_name: "node-executor-service"
      namespace_prefix: "ruh-catalyst" # This creates namespaces like ruh-catalyst-dev, ruh-catalyst-staging, etc.

      # Google Cloud configuration (using organization variables)
      project_id: ${{ vars.PROJECT_ID }}
      region: ${{ vars.REGION }}
      cluster_name: ${{ vars.CLUSTER_NAME }}
      repository: ${{ vars.REPOSITORY }}
      repo_url: ${{ vars.REPO_URL }}

      # Optional: Override default resource configurations for this service
      memory_request: "200Mi"
      cpu_request: "100m"
      memory_limit: "600Mi"
      cpu_limit: "250m"
      container_port: "50052"
      service_port: "80"

      # Optional: Custom ingress host (if not provided, will use default pattern)
      # ingress_host: 'node-executor-custom-domain.com'

    secrets:
      service_account_key: ${{ secrets.SERVICE_ACCOUNT_KEY }}
      git_token: ${{ secrets.GIT_TOKEN }}
      slack_webhook: ${{ secrets.SLACK_WEBHOOK_SECRET }}

#!/bin/bash

# GitHub Organization Setup Script
# This script helps set up GitHub organization variables and secrets for team-based CI/CD

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ORG_NAME=""
TEAMS=("ruh-catalyst" "ruh-aim" "ruh-commons")

print_header() {
    echo -e "${BLUE}================================${NC}"
    echo -e "${BLUE}  GitHub Organization Setup${NC}"
    echo -e "${BLUE}================================${NC}"
    echo
}

print_step() {
    echo -e "${GREEN}[STEP]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    print_step "Checking prerequisites..."
    
    # Check if gh CLI is installed
    if ! command -v gh &> /dev/null; then
        print_error "GitHub CLI (gh) is not installed. Please install it first:"
        echo "https://cli.github.com/"
        exit 1
    fi
    
    # Check if user is authenticated
    if ! gh auth status &> /dev/null; then
        print_error "You are not authenticated with GitHub CLI. Please run:"
        echo "gh auth login"
        exit 1
    fi
    
    echo -e "${GREEN}✓${NC} Prerequisites check passed"
}

get_organization() {
    if [ -z "$ORG_NAME" ]; then
        echo -n "Enter your GitHub organization name: "
        read ORG_NAME
    fi
    
    # Verify organization exists and user has access
    if ! gh api "orgs/$ORG_NAME" &> /dev/null; then
        print_error "Cannot access organization '$ORG_NAME'. Please check the name and your permissions."
        exit 1
    fi
    
    echo -e "${GREEN}✓${NC} Organization '$ORG_NAME' verified"
}

create_teams() {
    print_step "Creating GitHub teams..."
    
    for team in "${TEAMS[@]}"; do
        echo -n "Creating team @$ORG_NAME/$team... "
        
        # Check if team already exists
        if gh api "orgs/$ORG_NAME/teams/$team" &> /dev/null; then
            echo -e "${YELLOW}already exists${NC}"
        else
            # Create team
            gh api "orgs/$ORG_NAME/teams" \
                --method POST \
                --field name="$team" \
                --field description="$team team services and repositories" \
                --field privacy="closed" > /dev/null
            echo -e "${GREEN}created${NC}"
        fi
    done
}

setup_team_variables() {
    print_step "Setting up team-specific organization variables..."
    
    for team in "${TEAMS[@]}"; do
        team_upper=$(echo "$team" | tr '[:lower:]' '[:upper:]' | tr '-' '_')
        
        echo -e "\n${BLUE}Setting up variables for team: $team${NC}"
        
        # Prompt for team-specific values
        echo -n "Enter PROJECT_ID for $team: "
        read project_id
        
        echo -n "Enter REGION for $team (default: us-central1): "
        read region
        region=${region:-us-central1}
        
        echo -n "Enter CLUSTER_NAME for $team: "
        read cluster_name
        
        echo -n "Enter REPOSITORY for $team (default: $team): "
        read repository
        repository=${repository:-$team}
        
        echo -n "Enter REPO_URL for $team (default: https://github.com/$ORG_NAME): "
        read repo_url
        repo_url=${repo_url:-https://github.com/$ORG_NAME}
        
        # Set organization variables
        gh variable set "${team_upper}_PROJECT_ID" --body "$project_id" --org "$ORG_NAME"
        gh variable set "${team_upper}_REGION" --body "$region" --org "$ORG_NAME"
        gh variable set "${team_upper}_CLUSTER_NAME" --body "$cluster_name" --org "$ORG_NAME"
        gh variable set "${team_upper}_REPOSITORY" --body "$repository" --org "$ORG_NAME"
        gh variable set "${team_upper}_REPO_URL" --body "$repo_url" --org "$ORG_NAME"
        
        echo -e "${GREEN}✓${NC} Variables set for $team"
    done
}

setup_team_secrets() {
    print_step "Setting up team-specific organization secrets..."
    
    print_warning "You will need to provide the following secrets for each team:"
    print_warning "- Service Account Key (JSON format)"
    print_warning "- Slack Webhook URL"
    
    for team in "${TEAMS[@]}"; do
        team_upper=$(echo "$team" | tr '[:lower:]' '[:upper:]' | tr '-' '_')
        
        echo -e "\n${BLUE}Setting up secrets for team: $team${NC}"
        
        # Service Account Key
        echo "Enter the path to the service account key JSON file for $team:"
        read -r sa_key_path
        
        if [ -f "$sa_key_path" ]; then
            gh secret set "${team_upper}_SERVICE_ACCOUNT_KEY" --body-file "$sa_key_path" --org "$ORG_NAME"
            echo -e "${GREEN}✓${NC} Service account key set for $team"
        else
            print_error "File not found: $sa_key_path"
            echo "Skipping service account key for $team"
        fi
        
        # Slack Webhook
        echo -n "Enter Slack webhook URL for $team (or press Enter to skip): "
        read slack_webhook
        
        if [ -n "$slack_webhook" ]; then
            echo "$slack_webhook" | gh secret set "${team_upper}_SLACK_WEBHOOK" --org "$ORG_NAME"
            echo -e "${GREEN}✓${NC} Slack webhook set for $team"
        else
            echo "Skipping Slack webhook for $team"
        fi
    done
}

setup_global_secrets() {
    print_step "Setting up global organization secrets..."
    
    echo -n "Enter your GitHub Personal Access Token: "
    read -s git_token
    echo
    
    echo "$git_token" | gh secret set "GIT_TOKEN" --org "$ORG_NAME"
    echo -e "${GREEN}✓${NC} Global GIT_TOKEN secret set"
}

print_summary() {
    print_step "Setup Summary"
    
    echo -e "\n${GREEN}✓ Teams created:${NC}"
    for team in "${TEAMS[@]}"; do
        echo "  - @$ORG_NAME/$team"
    done
    
    echo -e "\n${GREEN}✓ Organization variables set:${NC}"
    for team in "${TEAMS[@]}"; do
        team_upper=$(echo "$team" | tr '[:lower:]' '[:upper:]' | tr '-' '_')
        echo "  - ${team_upper}_PROJECT_ID"
        echo "  - ${team_upper}_REGION"
        echo "  - ${team_upper}_CLUSTER_NAME"
        echo "  - ${team_upper}_REPOSITORY"
        echo "  - ${team_upper}_REPO_URL"
    done
    
    echo -e "\n${GREEN}✓ Organization secrets set:${NC}"
    echo "  - GIT_TOKEN (global)"
    for team in "${TEAMS[@]}"; do
        team_upper=$(echo "$team" | tr '[:lower:]' '[:upper:]' | tr '-' '_')
        echo "  - ${team_upper}_SERVICE_ACCOUNT_KEY"
        echo "  - ${team_upper}_SLACK_WEBHOOK"
    done
    
    echo -e "\n${BLUE}Next Steps:${NC}"
    echo "1. Add team members to the created teams"
    echo "2. Assign repositories to teams with appropriate permissions"
    echo "3. Copy the appropriate workflow template to your service repositories"
    echo "4. Test the CI/CD pipeline with a sample deployment"
    
    echo -e "\n${GREEN}Setup completed successfully!${NC}"
}

main() {
    print_header
    check_prerequisites
    get_organization
    create_teams
    setup_team_variables
    setup_team_secrets
    setup_global_secrets
    print_summary
}

# Run main function
main "$@"
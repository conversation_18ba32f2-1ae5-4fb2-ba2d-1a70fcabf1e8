apiVersion: v1
kind: ServiceAccount
metadata:
  name: <SERVICE_NAME>-sa
  namespace: <NAMESPACE>
  labels:
    name: <SERVICE_NAME>-sa
    namespace: <NAMESPACE>
    app: <SERVICE_NAME>
    deployment: <SERVICE_NAME>-dp
---
# Create Deployment
apiVersion: apps/v1
kind: Deployment
metadata:
  name: <SERVICE_NAME>-dp
  namespace: <NAMESPACE>
  labels:
    name: <SERVICE_NAME>-dp
    namespace: <NAMESPACE>
    app: <SERVICE_NAME>
    serviceaccount: <SERVICE_NAME>-sa
    deployment: <SERVICE_NAME>-dp
spec:
  replicas: 1
  selector:
    matchLabels:
      app: <SERVICE_NAME>
      deployment: <SERVICE_NAME>-dp
  template:
    metadata:
      labels:
        namespace: <NAMESPACE>
        app: <SERVICE_NAME>
        deployment: <SERVICE_NAME>-dp
    spec:
      serviceAccountName: <SERVICE_NAME>-sa
      containers:
        - name: <SERVICE_NAME>
          image: <GAR_HOSTNAME>/<PROJECT_ID>/<REPOSITORY>/<IMAGE_NAME>:<ENV>-<VERSION>
          resources:
            requests:
              memory: <MEMORY_REQUEST>
              cpu: <CPU_REQUEST>
            limits:
              memory: <MEMORY_LIMIT>
              cpu: <CPU_LIMIT>
          ports:
            - containerPort: <CONTAINER_PORT>
          # Uncomment and configure health checks as needed
          # readinessProbe:
          #   tcpSocket:
          #     port: <CONTAINER_PORT>
          #   initialDelaySeconds: 5
          #   periodSeconds: 10
          # livenessProbe:
          #   tcpSocket:
          #     port: <CONTAINER_PORT>
          #   initialDelaySeconds: 15
          #   periodSeconds: 20
          # Environment variables can be added here
          # env:
          # - name: ENV
          #   value: "<ENV>"
      # Uncomment for spot instance tolerations
      # tolerations:
      # - key: "spotInstance"
      #   operator: "Equal"
      #   value: "true"
      #   effect: "PreferNoSchedule"
      # nodeSelector:
      #   eks.amazonaws.com/capacityType: SPOT
---
#### Create Service
apiVersion: v1
kind: Service
metadata:
  name: <SERVICE_NAME>-svc
  namespace: <NAMESPACE>
spec:
  selector:
    app: <SERVICE_NAME>
    deployment: <SERVICE_NAME>-dp
  ports:
    - protocol: TCP
      port: <SERVICE_PORT>
      targetPort: <CONTAINER_PORT>
  sessionAffinityConfig:
    clientIP:
      timeoutSeconds: 100000
---
### Create HPA (Horizontal Pod Autoscaler) - Uncomment if needed
# apiVersion: autoscaling/v2
# kind: HorizontalPodAutoscaler
# metadata:
#   name: <SERVICE_NAME>-hpa
#   namespace: <NAMESPACE>
# spec:
#   scaleTargetRef:
#     apiVersion: apps/v1
#     kind: Deployment
#     name: <SERVICE_NAME>-dp
#   minReplicas: 1
#   maxReplicas: 3
#   metrics:
#     - type: Resource
#       resource:
#         name: cpu
#         target:
#           type: Utilization
#           averageUtilization: 60
#     - type: Resource
#       resource:
#         name: memory
#         target:
#           type: Utilization
#           averageUtilization: 70
---
### Create Nginx Ingress
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: <SERVICE_NAME>-ingress
  namespace: <NAMESPACE>
  annotations:
    # Add any required ingress annotations here
    # nginx.ingress.kubernetes.io/rewrite-target: /
    # cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  ingressClassName: nginx
  # Uncomment for TLS
  # tls:
  # - hosts:
  #   - <INGRESS_HOST>
  #   secretName: <SERVICE_NAME>-tls
  rules:
    - host: <INGRESS_HOST>
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: <SERVICE_NAME>-svc
                port:
                  number: <SERVICE_PORT>

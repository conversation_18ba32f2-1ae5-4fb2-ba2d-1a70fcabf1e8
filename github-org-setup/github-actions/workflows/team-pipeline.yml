name: Team-Aware CI/CD Pipeline
description: "Team-aware pipeline that automatically detects team and uses team-specific variables"

on:
  workflow_call:
    inputs:
      # Service configuration
      service_name:
        required: true
        type: string
        description: "Service name"
      team:
        required: true
        type: string
        description: "Team name (ruh-catalyst, ruh-aim, ruh-commons)"

      # Optional configurations with defaults
      gar_hostname:
        required: false
        type: string
        default: "us-central1-docker.pkg.dev"
        description: "Google Artifact Registry Hostname"

      # Resource configurations
      memory_request:
        required: false
        type: string
        default: "128Mi"
        description: "Memory request"
      cpu_request:
        required: false
        type: string
        default: "100m"
        description: "CPU request"
      memory_limit:
        required: false
        type: string
        default: "512Mi"
        description: "Memory limit"
      cpu_limit:
        required: false
        type: string
        default: "200m"
        description: "CPU limit"
      container_port:
        required: false
        type: string
        default: "8080"
        description: "Container port"
      service_port:
        required: false
        type: string
        default: "80"
        description: "Service port"
      ingress_host:
        required: false
        type: string
        description: "Custom ingress host (optional)"

    secrets:
      # Global secrets (shared across all teams)
      git_token:
        required: true
        description: "Git Token for private repositories"

      # Team-specific secrets (passed dynamically)
      team_service_account_key:
        required: true
        description: "Team-specific Google Cloud Service Account Key"
      team_slack_webhook:
        required: false
        description: "Team-specific Slack webhook URL"

jobs:
  setup-team-config:
    name: Setup Team Configuration
    runs-on: ubuntu-latest
    outputs:
      project_id: ${{ steps.team-config.outputs.project_id }}
      region: ${{ steps.team-config.outputs.region }}
      cluster_name: ${{ steps.team-config.outputs.cluster_name }}
      repository: ${{ steps.team-config.outputs.repository }}
      repo_url: ${{ steps.team-config.outputs.repo_url }}
      namespace_prefix: ${{ steps.team-config.outputs.namespace_prefix }}
    steps:
      - name: Configure team-specific variables
        id: team-config
        run: |
          case "${{ inputs.team }}" in
            "ruh-catalyst")
              echo "project_id=${{ vars.RUH_CATALYST_PROJECT_ID }}" >> $GITHUB_OUTPUT
              echo "region=${{ vars.RUH_CATALYST_REGION }}" >> $GITHUB_OUTPUT
              echo "cluster_name=${{ vars.RUH_CATALYST_CLUSTER_NAME }}" >> $GITHUB_OUTPUT
              echo "repository=${{ vars.RUH_CATALYST_REPOSITORY }}" >> $GITHUB_OUTPUT
              echo "repo_url=${{ vars.RUH_CATALYST_REPO_URL }}" >> $GITHUB_OUTPUT
              echo "namespace_prefix=ruh-catalyst" >> $GITHUB_OUTPUT
              ;;
            "ruh-aim")
              echo "project_id=${{ vars.RUH_AIM_PROJECT_ID }}" >> $GITHUB_OUTPUT
              echo "region=${{ vars.RUH_AIM_REGION }}" >> $GITHUB_OUTPUT
              echo "cluster_name=${{ vars.RUH_AIM_CLUSTER_NAME }}" >> $GITHUB_OUTPUT
              echo "repository=${{ vars.RUH_AIM_REPOSITORY }}" >> $GITHUB_OUTPUT
              echo "repo_url=${{ vars.RUH_AIM_REPO_URL }}" >> $GITHUB_OUTPUT
              echo "namespace_prefix=ruh-aim" >> $GITHUB_OUTPUT
              ;;
            "ruh-commons")
              echo "project_id=${{ vars.RUH_COMMONS_PROJECT_ID }}" >> $GITHUB_OUTPUT
              echo "region=${{ vars.RUH_COMMONS_REGION }}" >> $GITHUB_OUTPUT
              echo "cluster_name=${{ vars.RUH_COMMONS_CLUSTER_NAME }}" >> $GITHUB_OUTPUT
              echo "repository=${{ vars.RUH_COMMONS_REPOSITORY }}" >> $GITHUB_OUTPUT
              echo "repo_url=${{ vars.RUH_COMMONS_REPO_URL }}" >> $GITHUB_OUTPUT
              echo "namespace_prefix=ruh-commons" >> $GITHUB_OUTPUT
              ;;
            *)
              echo "Error: Unknown team '${{ inputs.team }}'"
              echo "Supported teams: ruh-catalyst, ruh-aim, ruh-commons"
              exit 1
              ;;
          esac

  setup:
    name: Setup Authentication
    needs: setup-team-config
    uses: ./.github/workflows/setup.yml
    with:
      project_id: ${{ needs.setup-team-config.outputs.project_id }}
      region: ${{ needs.setup-team-config.outputs.region }}
      cluster_name: ${{ needs.setup-team-config.outputs.cluster_name }}
    secrets:
      service_account_key: ${{ secrets.team_service_account_key }}

  build:
    name: Build Docker Image
    needs: [setup-team-config, setup]
    uses: ./.github/workflows/build.yml
    with:
      project_id: ${{ needs.setup-team-config.outputs.project_id }}
      repository: ${{ needs.setup-team-config.outputs.repository }}
      image_name: ${{ inputs.service_name }}
      gar_hostname: ${{ inputs.gar_hostname }}
      repo_url: ${{ needs.setup-team-config.outputs.repo_url }}
      environment: ${{ github.ref_name }}
    secrets:
      service_account_key: ${{ secrets.team_service_account_key }}
      git_token: ${{ secrets.git_token }}

  notify-build-failure:
    name: Notify Build Failure
    if: failure() && needs.build.result == 'failure'
    needs: [setup-team-config, build]
    uses: ./.github/workflows/notify.yml
    with:
      notification_type: "build_failure"
      service_name: ${{ inputs.service_name }}
      environment: ${{ github.ref_name }}
      status: "failure"
      custom_message: "Team: ${{ inputs.team }}"
    secrets:
      slack_webhook: ${{ secrets.team_slack_webhook }}

  deploy:
    name: Deploy to Kubernetes
    needs: [setup-team-config, setup, build]
    if: success()
    uses: ./.github/workflows/deploy.yml
    with:
      project_id: ${{ needs.setup-team-config.outputs.project_id }}
      region: ${{ needs.setup-team-config.outputs.region }}
      cluster_name: ${{ needs.setup-team-config.outputs.cluster_name }}
      repository: ${{ needs.setup-team-config.outputs.repository }}
      service_name: ${{ inputs.service_name }}
      namespace_prefix: ${{ needs.setup-team-config.outputs.namespace_prefix }}
      environment: ${{ github.ref_name }}
      image_tag: ${{ needs.build.outputs.image_tag }}
      gar_hostname: ${{ inputs.gar_hostname }}
      memory_request: ${{ inputs.memory_request }}
      cpu_request: ${{ inputs.cpu_request }}
      memory_limit: ${{ inputs.memory_limit }}
      cpu_limit: ${{ inputs.cpu_limit }}
      container_port: ${{ inputs.container_port }}
      service_port: ${{ inputs.service_port }}
      ingress_host: ${{ inputs.ingress_host }}
    secrets:
      service_account_key: ${{ secrets.team_service_account_key }}
      slack_webhook: ${{ secrets.team_slack_webhook }}

  notify-final:
    name: Final Status Notification
    if: always()
    needs: [setup-team-config, setup, build, deploy]
    uses: ./.github/workflows/notify.yml
    with:
      notification_type: "final_status"
      service_name: ${{ inputs.service_name }}
      environment: ${{ github.ref_name }}
      status: ${{ needs.deploy.result == 'success' && 'success' || 'failure' }}
      custom_message: "Team: ${{ inputs.team }}, Namespace: ${{ needs.setup-team-config.outputs.namespace_prefix }}-${{ github.ref_name }}"
    secrets:
      slack_webhook: ${{ secrets.team_slack_webhook }}

name: Setup Authentication
description: "Reusable workflow for setting up Google Cloud authentication"

on:
  workflow_call:
    inputs:
      project_id:
        required: true
        type: string
        description: "Google Cloud Project ID"
      region:
        required: true
        type: string
        description: "Google Cloud Region"
      cluster_name:
        required: true
        type: string
        description: "GKE Cluster Name"
    secrets:
      service_account_key:
        required: true
        description: "Google Cloud Service Account Key"
    outputs:
      gcloud_token:
        description: "Google Cloud access token"
        value: ${{ jobs.setup.outputs.gcloud_token }}

jobs:
  setup:
    runs-on: ubuntu-latest
    outputs:
      gcloud_token: ${{ steps.auth.outputs.access_token }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Google Cloud CLI
        uses: google-github-actions/setup-gcloud@v2
        with:
          service_account_key: ${{ secrets.service_account_key }}
          project_id: ${{ inputs.project_id }}

      - name: Authenticate with Google Cloud
        id: auth
        run: |
          echo '${{ secrets.service_account_key }}' > service-account-key.json
          gcloud auth activate-service-account --key-file=service-account-key.json --project=${{ inputs.project_id }}
          ACCESS_TOKEN=$(gcloud auth print-access-token)
          echo "access_token=$ACCESS_TOKEN" >> $GITHUB_OUTPUT

          # Create files directory for artifacts
          mkdir -p files
          echo "$ACCESS_TOKEN" > files/gcloud_token

      - name: Configure kubectl
        run: |
          gcloud container clusters get-credentials ${{ inputs.cluster_name }} --region ${{ inputs.region }} --project ${{ inputs.project_id }}

      - name: Upload authentication artifacts
        uses: actions/upload-artifact@v4
        with:
          name: gcloud-auth-${{ github.run_id }}
          path: files/
          retention-days: 1

name: Slack Notifications
description: "Reusable workflow for sending Slack notifications"

on:
  workflow_call:
    inputs:
      notification_type:
        required: true
        type: string
        description: "Type of notification: build_failure, deployment_status, or final_status"
      service_name:
        required: true
        type: string
        description: "Service name"
      environment:
        required: true
        type: string
        description: "Environment (dev/staging/main)"
      status:
        required: false
        type: string
        default: "unknown"
        description: "Job status (success/failure)"
      custom_message:
        required: false
        type: string
        description: "Custom message to include"
    secrets:
      slack_webhook:
        required: true
        description: "Slack webhook URL"

jobs:
  notify:
    runs-on: ubuntu-latest
    steps:
      - name: Send build failure notification
        if: inputs.notification_type == 'build_failure'
        run: |
          MESSAGE="🔨❌ Build failed for ${{ inputs.service_name }} on ${{ inputs.environment }} branch"
          if [ -n "${{ inputs.custom_message }}" ]; then
            MESSAGE="$MESSAGE - ${{ inputs.custom_message }}"
          fi
          MESSAGE="$MESSAGE. Check logs: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"

          curl -H "Content-type: application/json" \
            --data "{\"channel\": \"ruh-cicd\",\"text\": \"$MESSAGE\"}" \
            -X POST "${{ secrets.slack_webhook }}"

      - name: Send deployment status notification
        if: inputs.notification_type == 'deployment_status'
        run: |
          if [ "${{ inputs.status }}" = "success" ]; then
            STATUS_EMOJI="🚀✅"
            STATUS_TEXT="succeeded"
          else
            STATUS_EMOJI="🚀❌"
            STATUS_TEXT="failed"
          fi

          MESSAGE="$STATUS_EMOJI Deployment $STATUS_TEXT for ${{ inputs.service_name }} on ${{ inputs.environment }}"
          if [ -n "${{ inputs.custom_message }}" ]; then
            MESSAGE="$MESSAGE - ${{ inputs.custom_message }}"
          fi
          MESSAGE="$MESSAGE. Check logs: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"

          curl -X POST -H 'Content-type: application/json' \
            --data "{\"channel\":\"ruh-cicd\",\"text\":\"$MESSAGE\"}" \
            "${{ secrets.slack_webhook }}"

      - name: Send final status notification
        if: inputs.notification_type == 'final_status'
        run: |
          if [ "${{ inputs.status }}" = "success" ]; then
            STATUS_EMOJI="✅"
            STATUS_TEXT="succeeded"
          else
            STATUS_EMOJI="❌"
            STATUS_TEXT="failed"
          fi

          MESSAGE="$STATUS_EMOJI Pipeline $STATUS_TEXT! Service: ${{ inputs.service_name }}, Environment: ${{ inputs.environment }}"
          if [ -n "${{ inputs.custom_message }}" ]; then
            MESSAGE="$MESSAGE - ${{ inputs.custom_message }}"
          fi
          MESSAGE="$MESSAGE. Check logs: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"

          curl -X POST -H 'Content-type: application/json' \
            --data "{\"channel\":\"ruh-cicd\",\"text\":\"$MESSAGE\"}" \
            "${{ secrets.slack_webhook }}"

      - name: Send custom notification
        if: inputs.notification_type == 'custom'
        run: |
          MESSAGE="${{ inputs.custom_message }}"
          if [ -z "$MESSAGE" ]; then
            MESSAGE="Custom notification from ${{ inputs.service_name }} pipeline"
          fi

          curl -X POST -H 'Content-type: application/json' \
            --data "{\"channel\":\"ruh-cicd\",\"text\":\"$MESSAGE\"}" \
            "${{ secrets.slack_webhook }}"

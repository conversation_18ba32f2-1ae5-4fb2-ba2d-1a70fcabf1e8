name: Build Docker Image
description: "Reusable workflow for building and pushing Docker images to Google Artifact Registry"

on:
  workflow_call:
    inputs:
      project_id:
        required: true
        type: string
        description: "Google Cloud Project ID"
      repository:
        required: true
        type: string
        description: "Artifact Registry Repository Name"
      image_name:
        required: true
        type: string
        description: "Docker Image Name"
      gar_hostname:
        required: false
        type: string
        default: "us-central1-docker.pkg.dev"
        description: "Google Artifact Registry Hostname"
      repo_url:
        required: true
        type: string
        description: "Repository URL for build args"
      environment:
        required: true
        type: string
        description: "Environment (dev/staging/main)"
    secrets:
      service_account_key:
        required: true
        description: "Google Cloud Service Account Key"
      git_token:
        required: true
        description: "Git Token for private repositories"
    outputs:
      image_tag:
        description: "Full Docker image tag"
        value: ${{ jobs.build.outputs.image_tag }}

jobs:
  build:
    runs-on: ubuntu-latest
    outputs:
      image_tag: ${{ steps.build.outputs.image_tag }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download authentication artifacts
        uses: actions/download-artifact@v4
        with:
          name: gcloud-auth-${{ github.run_id }}
          path: files/

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Configure Docker to use gcloud as credential helper
        run: |
          gcloud auth configure-docker ${{ inputs.gar_hostname }}

      - name: Login to Google Artifact Registry
        run: |
          cat files/gcloud_token | docker login -u oauth2accesstoken --password-stdin https://${{ inputs.gar_hostname }}

      - name: Build and push Docker image
        id: build
        run: |
          # Generate image tag
          SHORT_SHA=$(echo ${{ github.sha }} | cut -c1-8)
          IMAGE_TAG="${{ inputs.gar_hostname }}/${{ inputs.project_id }}/${{ inputs.repository }}/${{ inputs.image_name }}:${{ inputs.environment }}-${SHORT_SHA}"

          echo "Building Docker image: $IMAGE_TAG"

          # Build Docker image
          docker build \
            --no-cache \
            --build-arg REPO_URL="${{ inputs.repo_url }}" \
            --build-arg GIT_TOKEN="${{ secrets.git_token }}" \
            --build-arg ENV="${{ inputs.environment }}" \
            -t "$IMAGE_TAG" .

          # Push Docker image
          docker push "$IMAGE_TAG"

          echo "image_tag=$IMAGE_TAG" >> $GITHUB_OUTPUT
          echo "Successfully built and pushed: $IMAGE_TAG"

      - name: Image scan (optional)
        continue-on-error: true
        run: |
          # Optional: Add container image scanning here
          echo "Image scanning can be added here"

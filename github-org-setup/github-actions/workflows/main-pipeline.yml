name: Main CI/CD Pipeline
description: "Main pipeline that orchestrates setup, build, deploy, and notifications"

on:
  workflow_call:
    inputs:
      # Service configuration
      service_name:
        required: true
        type: string
        description: "Service name"
      namespace_prefix:
        required: true
        type: string
        description: "Namespace prefix (e.g., ruh-catalyst, ruh-aim, ruh-commons)"

      # Google Cloud configuration
      project_id:
        required: true
        type: string
        description: "Google Cloud Project ID"
      region:
        required: true
        type: string
        description: "Google Cloud Region"
      cluster_name:
        required: true
        type: string
        description: "GKE Cluster Name"
      repository:
        required: true
        type: string
        description: "Artifact Registry Repository Name"
      repo_url:
        required: true
        type: string
        description: "Repository URL for build args"

      # Optional configurations with defaults
      gar_hostname:
        required: false
        type: string
        default: "us-central1-docker.pkg.dev"
        description: "Google Artifact Registry Hostname"

      # Resource configurations
      memory_request:
        required: false
        type: string
        default: "128Mi"
        description: "Memory request"
      cpu_request:
        required: false
        type: string
        default: "100m"
        description: "CPU request"
      memory_limit:
        required: false
        type: string
        default: "512Mi"
        description: "Memory limit"
      cpu_limit:
        required: false
        type: string
        default: "200m"
        description: "CPU limit"
      container_port:
        required: false
        type: string
        default: "8080"
        description: "Container port"
      service_port:
        required: false
        type: string
        default: "80"
        description: "Service port"
      ingress_host:
        required: false
        type: string
        description: "Custom ingress host (optional)"

    secrets:
      service_account_key:
        required: true
        description: "Google Cloud Service Account Key"
      git_token:
        required: true
        description: "Git Token for private repositories"
      slack_webhook:
        required: false
        description: "Slack webhook URL for notifications"

jobs:
  setup:
    name: Setup Authentication
    uses: ./.github/workflows/setup.yml
    with:
      project_id: ${{ inputs.project_id }}
      region: ${{ inputs.region }}
      cluster_name: ${{ inputs.cluster_name }}
    secrets:
      service_account_key: ${{ secrets.service_account_key }}

  build:
    name: Build Docker Image
    needs: setup
    uses: ./.github/workflows/build.yml
    with:
      project_id: ${{ inputs.project_id }}
      repository: ${{ inputs.repository }}
      image_name: ${{ inputs.service_name }}
      gar_hostname: ${{ inputs.gar_hostname }}
      repo_url: ${{ inputs.repo_url }}
      environment: ${{ github.ref_name }}
    secrets:
      service_account_key: ${{ secrets.service_account_key }}
      git_token: ${{ secrets.git_token }}

  notify-build-failure:
    name: Notify Build Failure
    if: failure() && needs.build.result == 'failure'
    needs: [build]
    uses: ./.github/workflows/notify.yml
    with:
      notification_type: "build_failure"
      service_name: ${{ inputs.service_name }}
      environment: ${{ github.ref_name }}
      status: "failure"
    secrets:
      slack_webhook: ${{ secrets.slack_webhook }}

  deploy:
    name: Deploy to Kubernetes
    needs: [setup, build]
    if: success()
    uses: ./.github/workflows/deploy.yml
    with:
      project_id: ${{ inputs.project_id }}
      region: ${{ inputs.region }}
      cluster_name: ${{ inputs.cluster_name }}
      repository: ${{ inputs.repository }}
      service_name: ${{ inputs.service_name }}
      namespace_prefix: ${{ inputs.namespace_prefix }}
      environment: ${{ github.ref_name }}
      image_tag: ${{ needs.build.outputs.image_tag }}
      gar_hostname: ${{ inputs.gar_hostname }}
      memory_request: ${{ inputs.memory_request }}
      cpu_request: ${{ inputs.cpu_request }}
      memory_limit: ${{ inputs.memory_limit }}
      cpu_limit: ${{ inputs.cpu_limit }}
      container_port: ${{ inputs.container_port }}
      service_port: ${{ inputs.service_port }}
      ingress_host: ${{ inputs.ingress_host }}
    secrets:
      service_account_key: ${{ secrets.service_account_key }}
      slack_webhook: ${{ secrets.slack_webhook }}

  notify-final:
    name: Final Status Notification
    if: always()
    needs: [setup, build, deploy]
    uses: ./.github/workflows/notify.yml
    with:
      notification_type: "final_status"
      service_name: ${{ inputs.service_name }}
      environment: ${{ github.ref_name }}
      status: ${{ needs.deploy.result == 'success' && 'success' || 'failure' }}
    secrets:
      slack_webhook: ${{ secrets.slack_webhook }}

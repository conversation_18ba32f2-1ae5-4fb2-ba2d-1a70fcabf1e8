name: Deploy to Kubernetes
description: "Reusable workflow for deploying applications to GKE"

on:
  workflow_call:
    inputs:
      project_id:
        required: true
        type: string
        description: "Google Cloud Project ID"
      region:
        required: true
        type: string
        description: "Google Cloud Region"
      cluster_name:
        required: true
        type: string
        description: "GKE Cluster Name"
      repository:
        required: true
        type: string
        description: "Artifact Registry Repository Name"
      service_name:
        required: true
        type: string
        description: "Kubernetes Service Name"
      namespace_prefix:
        required: true
        type: string
        description: "Namespace prefix (e.g., ruh-catalyst)"
      environment:
        required: true
        type: string
        description: "Environment (dev/staging/main)"
      image_tag:
        required: true
        type: string
        description: "Full Docker image tag from build job"
      gar_hostname:
        required: false
        type: string
        default: "us-central1-docker.pkg.dev"
        description: "Google Artifact Registry Hostname"
      # Resource configurations
      memory_request:
        required: false
        type: string
        default: "128Mi"
        description: "Memory request"
      cpu_request:
        required: false
        type: string
        default: "100m"
        description: "CPU request"
      memory_limit:
        required: false
        type: string
        default: "512Mi"
        description: "Memory limit"
      cpu_limit:
        required: false
        type: string
        default: "200m"
        description: "CPU limit"
      container_port:
        required: false
        type: string
        default: "8080"
        description: "Container port"
      service_port:
        required: false
        type: string
        default: "80"
        description: "Service port"
      ingress_host:
        required: false
        type: string
        description: "Custom ingress host (optional)"
    secrets:
      service_account_key:
        required: true
        description: "Google Cloud Service Account Key"
      slack_webhook:
        required: false
        description: "Slack webhook URL for notifications"

jobs:
  deploy:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Google Cloud CLI
        uses: google-github-actions/setup-gcloud@v2
        with:
          service_account_key: ${{ secrets.service_account_key }}
          project_id: ${{ inputs.project_id }}

      - name: Authenticate and configure kubectl
        run: |
          echo '${{ secrets.service_account_key }}' > service-account-key.json
          gcloud auth activate-service-account --key-file=service-account-key.json --project=${{ inputs.project_id }}
          gcloud container clusters get-credentials ${{ inputs.cluster_name }} --region ${{ inputs.region }} --project=${{ inputs.project_id }}

      - name: Set deployment variables
        id: vars
        run: |
          # Set namespace with prefix and environment
          NAMESPACE="${{ inputs.namespace_prefix }}-${{ inputs.environment }}"
          echo "namespace=$NAMESPACE" >> $GITHUB_OUTPUT

          # Set ingress host
          if [ -n "${{ inputs.ingress_host }}" ]; then
            INGRESS_HOST="${{ inputs.ingress_host }}"
          else
            INGRESS_HOST="${{ inputs.service_name }}-${NAMESPACE}.rapidinnovation.dev"
          fi
          echo "ingress_host=$INGRESS_HOST" >> $GITHUB_OUTPUT

          # Extract image details from full tag
          SHORT_SHA=$(echo ${{ github.sha }} | cut -c1-8)
          echo "short_sha=$SHORT_SHA" >> $GITHUB_OUTPUT

      - name: Create dynamic Kubernetes manifest
        run: |
          # Copy template and create environment-specific manifest
          cp k8s-manifest-template.yml k8s-manifest-${{ inputs.environment }}.yml

          # Replace placeholders in the manifest
          sed -i \
            -e "s|<SERVICE_NAME>|${{ inputs.service_name }}|g" \
            -e "s|<NAMESPACE>|${{ steps.vars.outputs.namespace }}|g" \
            -e "s|<GAR_HOSTNAME>|${{ inputs.gar_hostname }}|g" \
            -e "s|<PROJECT_ID>|${{ inputs.project_id }}|g" \
            -e "s|<REPOSITORY>|${{ inputs.repository }}|g" \
            -e "s|<IMAGE_NAME>|${{ inputs.service_name }}|g" \
            -e "s|<ENV>|${{ inputs.environment }}|g" \
            -e "s|<VERSION>|${{ steps.vars.outputs.short_sha }}|g" \
            -e "s|<MEMORY_REQUEST>|${{ inputs.memory_request }}|g" \
            -e "s|<CPU_REQUEST>|${{ inputs.cpu_request }}|g" \
            -e "s|<MEMORY_LIMIT>|${{ inputs.memory_limit }}|g" \
            -e "s|<CPU_LIMIT>|${{ inputs.cpu_limit }}|g" \
            -e "s|<CONTAINER_PORT>|${{ inputs.container_port }}|g" \
            -e "s|<SERVICE_PORT>|${{ inputs.service_port }}|g" \
            -e "s|<INGRESS_HOST>|${{ steps.vars.outputs.ingress_host }}|g" \
            "k8s-manifest-${{ inputs.environment }}.yml"

      - name: Create namespace if it doesn't exist
        run: |
          kubectl create namespace ${{ steps.vars.outputs.namespace }} --dry-run=client -o yaml | kubectl apply -f -

      - name: Deploy to Kubernetes
        run: |
          # Check if deployment exists
          if kubectl get deployment -n ${{ steps.vars.outputs.namespace }} ${{ inputs.service_name }}-dp --no-headers --ignore-not-found | grep -q .; then
            echo "Deployment exists, performing rolling update"
            kubectl set image deployment/${{ inputs.service_name }}-dp \
              ${{ inputs.service_name }}=${{ inputs.image_tag }} \
              -n ${{ steps.vars.outputs.namespace }}
          else
            echo "Deployment not found, creating new deployment"
            kubectl apply -f k8s-manifest-${{ inputs.environment }}.yml
          fi

      - name: Wait for deployment to be ready
        run: |
          kubectl rollout status deployment/${{ inputs.service_name }}-dp -n ${{ steps.vars.outputs.namespace }} --timeout=300s

      - name: Get deployment info
        run: |
          echo "Deployment successful!"
          kubectl get pods -n ${{ steps.vars.outputs.namespace }} -l app=${{ inputs.service_name }}
          kubectl get svc -n ${{ steps.vars.outputs.namespace }} -l app=${{ inputs.service_name }}

      - name: Send Slack notification
        if: always() && secrets.slack_webhook != ''
        run: |
          if [ "${{ job.status }}" = "success" ]; then
            STATUS_EMOJI="✅"
            STATUS_TEXT="succeeded"
          else
            STATUS_EMOJI="❌"
            STATUS_TEXT="failed"
          fi

          MESSAGE="${STATUS_EMOJI} Deployment ${STATUS_TEXT}! Service: ${{ inputs.service_name }}, Environment: ${{ inputs.environment }}, Namespace: ${{ steps.vars.outputs.namespace }}. Check the logs: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"

          curl -X POST -H 'Content-type: application/json' \
            --data "{\"channel\":\"ruh-cicd\",\"text\":\"${MESSAGE}\"}" \
            "${{ secrets.slack_webhook }}"

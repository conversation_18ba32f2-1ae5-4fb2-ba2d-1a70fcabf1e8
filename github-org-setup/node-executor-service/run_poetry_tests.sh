#!/bin/bash
# Shell script to run the error response tests using Poetry

echo -e "\033[1;36m=== Running Error Response Tests with Poetry ===\033[0m"

# Test 1: Success Test
echo -e "\n\033[1;32m=== Running Test 1: send_error_response Success Test ===\033[0m"
poetry run python tests/test_send_error_response_success.py

# Test 2: Failure Test
echo -e "\n\033[1;32m=== Running Test 2: send_error_response Failure Handling Test ===\033[0m"
poetry run python tests/test_send_error_response_failure.py

# Test 3: API Test
echo -e "\n\033[1;32m=== Running Test 3: API Component Error Response Test ===\033[0m"
poetry run python tests/test_api_error_response.py

# Run all tests together
echo -e "\n\033[1;32m=== Running All Tests Together ===\033[0m"
poetry run python tests/run_error_response_tests.py

echo -e "\n\033[1;36m=== All tests completed ===\033[0m"

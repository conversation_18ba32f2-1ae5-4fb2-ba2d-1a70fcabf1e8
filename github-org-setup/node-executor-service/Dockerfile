# Build stage
FROM python:3.12-slim AS builder

# Set environment variables (customize if needed)
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    POETRY_VERSION=1.7.1 \
    POETRY_HOME="/opt/poetry" \
    POETRY_VIRTUALENVS_CREATE=false


# Install system dependencies (customize if needed - build-essential often needed)
RUN apt-get update \
    && apt-get install -y --no-install-recommends \
    curl \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Install poetry
RUN curl -sSL https://install.python-poetry.org | python3 -

# Add poetry to PATH
ENV PATH="$POETRY_HOME/bin:$PATH"

# Set working directory (customize if needed - usually /app is good)
WORKDIR /app

# Copy poetry files
COPY pyproject.toml poetry.lock ./

# Install dependencies
RUN poetry install --no-interaction --no-ansi --no-root

# Runtime stage
FROM python:3.12-slim

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PYTHONPATH=/app

# Create non-root user
RUN groupadd -r nodeexec && \
    useradd -r -g nodeexec -d /app -s /bin/bash nodeexec && \
    mkdir -p /app/data /app/logs && \
    chown -R nodeexec:nodeexec /app

# Install runtime dependencies only
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
    tini && \
    apt-get clean && \
    rm -rf /var/lib/apt/lists/*

# Set working directory
WORKDIR /app

# Copy from builder stage
COPY --from=builder /usr/local/lib/python3.12/site-packages /usr/local/lib/python3.12/site-packages
COPY --from=builder /usr/local/bin /usr/local/bin

# Copy application code
COPY --chown=nodeexec:nodeexec app /app/app
COPY --chown=nodeexec:nodeexec .env.example /app/.env

# Create necessary directories with proper permissions
RUN mkdir -p /app/data /app/logs && \
    chown -R nodeexec:nodeexec /app/data /app/logs

# Switch to non-root user
USER nodeexec

# Use tini as init system
ENTRYPOINT ["tini", "--"]

# Run the application
CMD ["python", "-m", "app.main"]

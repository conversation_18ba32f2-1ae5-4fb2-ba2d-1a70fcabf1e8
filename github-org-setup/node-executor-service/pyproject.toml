[tool.poetry]
name = "api-node-executor"
version = "0.1.0"
description = "Service responsible for executing API node requests"
authors = ["PratyushNag"]
packages = [{include = "app"}]

[tool.poetry.dependencies]
python = "^3.12"
aiokafka = "^0.12.0"
werkzeug = "^3.1.3"
pydantic-settings = "^2.8.1"
aiohttp = "^3.9.1"
pydantic = "^2.11.3"
requests = "^2.32.3"
kafka-python = "^2.2.0"
pandas = "^2.0.0"
openai = "^1.0.0"

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.0"
pytest-asyncio = "^0.21.1"
pytest-xdist = "^3.6.1"
fastapi = "^0.104.1"
uvicorn = "^0.24.0"
httpx = "^0.25.1"


[tool.poetry.scripts]
node-executor = "app.auto_main:main_entry"

[build-system]
requires = ["poetry-core>=2.0.0,<3.0.0"]
build-backend = "poetry.core.masonry.api"

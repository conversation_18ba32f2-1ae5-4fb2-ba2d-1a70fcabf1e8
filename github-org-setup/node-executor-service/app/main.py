"""
Auto Main - Automatically discovers and runs all components.
"""
import asyncio
import logging
import os
import signal
import sys
import traceback
from app.config.config import settings
import datetime

from app.core_.component_system import get_component_manager
# Removed security_config, security_middleware, audit_logger imports
# from app.core_.security_config import get_security_settings
# from app.core_.security_middleware import get_security_middleware
# from app.core_.audit_logger import setup_audit_logging, log_security_event

# Import the logger configuration
try:
    from app.utils.logging_config import setup_logger
    logger = setup_logger("NodeExecutor")
except ImportError:
    # Fallback to basic logging if logging_config is not available
    # Create logs directory
    today = datetime.datetime.now().strftime("%Y-%m-%d")
    log_dir = os.path.join("logs", today)
    os.makedirs(log_dir, exist_ok=True)

    # Create a timestamp for the log file
    timestamp = datetime.datetime.now().strftime("%H-%M-%S")
    log_file = os.path.join(log_dir, f"NodeExecutor_{timestamp}.log")

    # Configure file handler
    file_handler = logging.FileHandler(log_file)
    file_handler.setLevel(logging.DEBUG)  # Set to DEBUG for more detailed logs

    # Configure console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.DEBUG)  # Set to DEBUG for more detailed logs

    # Create formatter
    formatter = logging.Formatter(
        "%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )

    # Set formatter for handlers
    file_handler.setFormatter(formatter)
    console_handler.setFormatter(formatter)

    # Configure root logger
    logging.basicConfig(
        level=logging.DEBUG,  # Set to DEBUG for more detailed logs
        handlers=[file_handler, console_handler],
        format="%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    logger = logging.getLogger(__name__)
    logger.info(f"Logging to file: {log_file}")


async def main():
    """
    Main entry point.

    Discovers and runs all components.
    """
    logger.info("Starting Node Executor")
    logger.info(f"Python version: {sys.version}")
    logger.info(f"Current working directory: {os.getcwd()}")

    try:

        kafka_bootstrap_servers = settings.kafka_bootstrap_servers
        logger.info(f"Using Kafka bootstrap servers: {kafka_bootstrap_servers}")

        # Get component manager
        logger.info("Initializing component manager")
        manager = get_component_manager(kafka_bootstrap_servers)
        logger.info("Component manager initialized")

        # Set up signal handlers for graceful shutdown
        logger.info("Setting up signal handlers for graceful shutdown")
        loop = asyncio.get_running_loop()
        shutdown_future = loop.create_future()
        logger.debug("Created shutdown future")

        def signal_handler(signame):
            """Handle signals for graceful shutdown."""
            logger.warning(f"Received signal {signame}. Initiating graceful shutdown...")
            if not shutdown_future.done():
                shutdown_future.set_result(True)
                logger.debug("Set shutdown future result to True")

        # Register signal handlers
        for sig in (signal.SIGINT, signal.SIGTERM):
            try:
                loop.add_signal_handler(sig, lambda s=sig: signal_handler(s.name))
                logger.debug(f"Registered signal handler for {sig.name}")
            except NotImplementedError:
                logger.warning(f"Cannot add signal handler for {sig.name} on this platform.")

    except Exception as e:
        logger.error(f"Error during initialization: {str(e)}")
        logger.debug(f"Exception details: {traceback.format_exc()}")
        raise

    try:
        # Dynamically discover and import all components
        logger.info("Dynamically discovering and importing components")
        try:
            # Import the discovery function
            from app.core_.component_system import discover_component_modules, COMPONENT_REGISTRY

            # Discover and import all component modules
            imported_modules = discover_component_modules()
            logger.info(f"Dynamically imported {len(imported_modules)} component modules")

            # Log the component registry after imports
            logger.info(f"Component registry after dynamic imports: {list(COMPONENT_REGISTRY.keys())}")

            # Verify specific components are registered
            expected_components = ["SelectDataComponent", "ApiRequestNode", "CombineTextComponent"]
            for component in expected_components:
                if component in COMPONENT_REGISTRY:
                    logger.info(f"✓ {component} is registered")
                else:
                    logger.error(f"✗ {component} is NOT registered")

            logger.info("Components imported successfully")
        except Exception as e:
            logger.error(f"Error importing components: {str(e)}")
            logger.debug(f"Exception details: {traceback.format_exc()}")

        # Log registered components
        components = list(manager.components.keys())
        logger.info(f"Registered components: {components}")
        logger.debug(f"Component details: {manager.components}")

        # Import the ToolExecutor to ensure it's initialized
        logger.info("Initializing ToolExecutor")
        from app.core_.tool_executor import get_tool_executor
        tool_executor = get_tool_executor()
        logger.info("ToolExecutor initialized successfully")

        # Start a single component to handle all tool requests
        logger.info("Starting the ApiRequestNode component as the main entry point for tool requests")
        await manager.start_component("ApiRequestNode")
        logger.info("ApiRequestNode component started successfully")

        # Log information about the tool-based architecture
        logger.info("Node Executor is now running in tool-based mode")
        logger.info("All components can be accessed using the tool_name parameter in requests")
        logger.info(f"Available tools: {list(COMPONENT_REGISTRY.keys())}")

        logger.info("Node Executor is now running. Press Ctrl+C to stop.")

        # Wait for shutdown signal
        logger.debug("Waiting for shutdown signal")
        await shutdown_future
        logger.debug("Shutdown signal received")

    except Exception as e:
        logger.error(f"Error in main: {str(e)}")
        logger.debug(f"Exception details: {traceback.format_exc()}")
    finally:
        logger.info("Initiating shutdown sequence")

        try:
            logger.info("Stopping all components")
            await manager.stop_all_components()
            logger.info("All components stopped successfully")
        except Exception as stop_error:
            logger.error(f"Error stopping components: {str(stop_error)}")
            logger.debug(f"Exception details: {traceback.format_exc()}")

        logger.info("Shutdown complete")


def main_entry():
    """
    Entry point for the Node Executor application.
    This function is called when running the application as a module or script.
    """
    logger.info("Node Executor application starting")

    try:
        logger.debug("Running main async function")
        asyncio.run(main())
        logger.debug("Main async function completed")
    except KeyboardInterrupt:
        logger.info("Process interrupted by KeyboardInterrupt (Ctrl+C)")
    except Exception as e:
        logger.critical(f"Service failed to run: {str(e)}")
        logger.debug(f"Exception details: {traceback.format_exc()}")
        sys.exit(1)

    logger.info("Node Executor application exited")


if __name__ == "__main__":
    main_entry()

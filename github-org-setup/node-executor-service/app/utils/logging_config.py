"""
Logging configuration for the application.
"""

import os
import logging
import datetime
import json
import asyncio
import threading
import traceback
import contextvars
from logging.handlers import <PERSON><PERSON>tingFileHandler
from typing import Dict, Any, List, Optional, Generator
from queue import Queue
from contextlib import contextmanager

from aiokafka import AIOKafkaProducer
from app.config.config import settings

# Context variables to store the current request_id and correlation_id
_request_id_var = contextvars.ContextVar("request_id", default=None)
_correlation_id_var = contextvars.ContextVar("correlation_id", default=None)


class RequestContext:
    """
    A class to manage request context information like request_id and correlation_id.
    Uses contextvars for async-safe context management.
    """

    @staticmethod
    def get_request_id() -> Optional[str]:
        """Get the current request_id from context"""
        return _request_id_var.get()

    @staticmethod
    def set_request_id(request_id: str) -> None:
        """Set the current request_id in context"""
        _request_id_var.set(request_id)

    @staticmethod
    def get_correlation_id() -> Optional[str]:
        """Get the current correlation_id from context"""
        return _correlation_id_var.get()

    @staticmethod
    def set_correlation_id(correlation_id: str) -> None:
        """Set the current correlation_id in context"""
        _correlation_id_var.set(correlation_id)

    @staticmethod
    @contextmanager
    def request_context(request_id: str, correlation_id: Optional[str] = None):
        """
        Context manager for setting request_id and optionally correlation_id for a block of code.

        Example:
            with RequestContext.request_context("req-123", "corr-456"):
                # All code in this block will have access to this request_id and correlation_id
                logger.info("Processing request")
        """
        req_token = _request_id_var.set(request_id)
        corr_token = None
        if correlation_id:
            corr_token = _correlation_id_var.set(correlation_id)
        try:
            yield
        finally:
            _request_id_var.reset(req_token)
            if corr_token:
                _correlation_id_var.reset(corr_token)


class RequestIdAdapter(logging.LoggerAdapter):
    """
    A LoggerAdapter that automatically adds request_id and correlation_id to log records.
    """

    def process(self, msg, kwargs):
        # Get or create extra dict
        kwargs_extra = kwargs.get("extra", {})

        # Add request_id from context if not already in extra
        if "request_id" not in kwargs_extra:
            request_id = RequestContext.get_request_id()
            if request_id:
                kwargs_extra["request_id"] = request_id

        # Add correlation_id from context if not already in extra
        if "correlation_id" not in kwargs_extra:
            correlation_id = RequestContext.get_correlation_id()
            if correlation_id:
                kwargs_extra["correlation_id"] = correlation_id

        # Add correlation_id if provided in extra
        if "correlation_id" in self.extra:
            kwargs_extra["correlation_id"] = self.extra["correlation_id"]

        # Update kwargs with our modified extra
        kwargs["extra"] = kwargs_extra
        return msg, kwargs


def get_logger_with_request_id(logger_name):
    """
    Get a logger that automatically includes request_id in all log messages.

    Args:
        logger_name: The name of the logger

    Returns:
        A logger adapter that includes request_id
    """
    logger = logging.getLogger(logger_name)
    return RequestIdAdapter(logger, {})


class KafkaLogHandler(logging.Handler):
    """
    A logging handler that sends logs to a Kafka topic.

    This handler runs a background thread with an event loop to handle
    asynchronous Kafka operations.
    """

    def __init__(
        self,
        bootstrap_servers: str,
        topic: str,
        level: int = logging.INFO,
        batch_size: int = 100,
        flush_interval: float = 5.0,
    ):
        """
        Initialize the Kafka log handler.

        Args:
            bootstrap_servers: Kafka bootstrap servers
            topic: Kafka topic to send logs to
            level: Logging level
            batch_size: Number of logs to batch before sending
            flush_interval: Time in seconds to flush logs if batch size not reached
        """
        super().__init__(level)
        self.bootstrap_servers = bootstrap_servers
        self.topic = topic
        self.batch_size = batch_size
        self.flush_interval = flush_interval

        # Queue for log records
        self.queue: Queue = Queue()

        # Producer and event loop
        self.producer = None
        self.loop = None
        self.thread = None
        self.running = False

        # Start the background thread
        self._start_background_thread()

    def _start_background_thread(self):
        """Start the background thread for async operations."""
        self.running = True
        self.thread = threading.Thread(target=self._run_async_loop, daemon=True)
        self.thread.start()

    def _run_async_loop(self):
        """Run the async event loop in the background thread."""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)

        try:
            self.loop.run_until_complete(self._process_logs())
        except Exception as e:
            print(f"Error in Kafka log handler: {e}")
            traceback.print_exc()
        finally:
            self.loop.close()

    async def _initialize_producer(self):
        """Initialize the Kafka producer."""
        if self.producer is None:
            self.producer = AIOKafkaProducer(
                bootstrap_servers=self.bootstrap_servers,
                value_serializer=lambda v: json.dumps(v).encode("utf-8"),
                acks="all",
                request_timeout_ms=settings.kafka_producer_request_timeout_ms,
                enable_idempotence=True,
            )
            await self.producer.start()

    async def _process_logs(self):
        """Process logs from the queue and send them to Kafka."""
        await self._initialize_producer()

        batch: List[Dict[str, Any]] = []
        last_flush_time = datetime.datetime.now()

        while self.running:
            # Check if we need to flush based on time
            now = datetime.datetime.now()
            time_since_flush = (now - last_flush_time).total_seconds()

            # Process queue until batch is full or timeout occurs
            try:
                # Non-blocking get with timeout
                while (
                    len(batch) < self.batch_size
                    and time_since_flush < self.flush_interval
                ):
                    try:
                        record = self.queue.get(block=True, timeout=0.1)
                        log_entry = self._format_log_record(record)
                        batch.append(log_entry)
                        self.queue.task_done()
                    except Exception:
                        # Queue empty or other error
                        break

                    # Update time since last flush
                    now = datetime.datetime.now()
                    time_since_flush = (now - last_flush_time).total_seconds()

                # Send batch if not empty
                if batch:
                    try:
                        for log_entry in batch:
                            # Prepare headers if correlation_id is present
                            headers = None
                            if "correlation_id" in log_entry:
                                correlation_id = log_entry.get("correlation_id")
                                headers = [
                                    ("correlationId", correlation_id.encode("utf-8"))
                                ]
                                print(
                                    f"Adding correlation_id {correlation_id} to Kafka headers"
                                )

                            # Send message with headers if available
                            if headers:
                                await self.producer.send_and_wait(
                                    self.topic, log_entry, headers=headers
                                )
                            else:
                                await self.producer.send_and_wait(self.topic, log_entry)

                        last_flush_time = datetime.datetime.now()
                    except Exception as e:
                        print(f"Error sending logs to Kafka: {e}")
                    finally:
                        batch = []

                # Small sleep to prevent CPU spinning
                await asyncio.sleep(0.01)

            except Exception as e:
                print(f"Error in Kafka log handler loop: {e}")
                await asyncio.sleep(1)  # Sleep on error to prevent tight loop

    def _format_log_record(self, record: logging.LogRecord) -> Dict[str, Any]:
        """Format a log record as a dictionary for Kafka."""
        log_entry = {
            "timestamp": datetime.datetime.fromtimestamp(record.created).strftime(
                "%Y-%m-%d %H:%M:%S"
            ),
            "logger": record.name,
            "level": record.levelname,
            "message": self.format(record),
            "function": record.funcName,
            "line": record.lineno,
            "thread": record.threadName,
            "process": record.process,
        }

        # Add request_id if available in extra attributes
        if hasattr(record, "request_id"):
            log_entry["request_id"] = record.request_id

        # Check for request_id in the LogRecord's extra dict
        if (
            hasattr(record, "extra")
            and isinstance(record.extra, dict)
            and "request_id" in record.extra
        ):
            log_entry["request_id"] = record.extra["request_id"]

        # Add correlation_id if available in extra attributes
        if hasattr(record, "correlation_id"):
            log_entry["correlation_id"] = record.correlation_id

        # Check for correlation_id in the LogRecord's extra dict
        if (
            hasattr(record, "extra")
            and isinstance(record.extra, dict)
            and "correlation_id" in record.extra
        ):
            log_entry["correlation_id"] = record.extra["correlation_id"]

        # Add exception info if available
        if record.exc_info:
            log_entry["exception"] = {
                "type": record.exc_info[0].__name__,
                "message": str(record.exc_info[1]),
                "traceback": traceback.format_exception(*record.exc_info),
            }

        return log_entry

    def emit(self, record: logging.LogRecord):
        """
        Emit a log record by adding it to the queue.

        Args:
            record: The log record to emit
        """
        try:
            self.queue.put(record)
        except Exception:
            self.handleError(record)

    def close(self):
        """Close the handler and stop the background thread."""
        self.running = False
        if self.thread:
            self.thread.join(timeout=5)

        # Close the producer in the event loop
        if self.loop and self.producer:
            try:
                # Create a new event loop if needed
                if not asyncio.get_event_loop().is_running():
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(self.producer.stop())
                    loop.close()
                else:
                    # Use run_coroutine_threadsafe if the loop is already running
                    future = asyncio.run_coroutine_threadsafe(self.producer.stop(), self.loop)
                    future.result(timeout=5)
            except Exception as e:
                # Log the exception but don't raise it
                print(f"Error closing Kafka producer: {e}")

        super().close()


def setup_logger(
    logger_name,
    log_level=logging.INFO,
    use_kafka=False,
    kafka_topic="workflow-responses",
    with_request_id=True,
):
    """
    Set up a logger with file, console, and optionally Kafka handlers.

    Args:
        logger_name: The name of the logger
        log_level: The logging level
        use_kafka: Whether to use Kafka logging
        kafka_topic: The Kafka topic to send logs to
        with_request_id: Whether to wrap the logger with RequestIdAdapter

    Returns:
        The configured logger, optionally wrapped with RequestIdAdapter
    """
    # Create the logger
    logger = logging.getLogger(logger_name)
    logger.setLevel(log_level)

    # Remove any existing handlers
    for handler in logger.handlers[:]:
        logger.removeHandler(handler)

    # Create a formatter that includes request_id and correlation_id if available
    class RequestIdFormatter(logging.Formatter):
        def format(self, record):
            # Handle request_id
            has_request_id = hasattr(record, "request_id") and record.request_id
            has_correlation_id = (
                hasattr(record, "correlation_id") and record.correlation_id
            )

            if not has_request_id and not has_correlation_id:
                # Set empty values to avoid KeyError
                record.request_id = ""
                record.correlation_id = ""

                # Use a format string without the ID sections
                original_fmt = self._style._fmt
                modified_fmt = original_fmt.replace("[ReqID:%(request_id)s] ", "")
                modified_fmt = modified_fmt.replace("[CorrID:%(correlation_id)s] ", "")
                self._style._fmt = modified_fmt

                result = super().format(record)

                # Restore the original format
                self._style._fmt = original_fmt
                return result
            elif not has_request_id:
                record.request_id = ""
                original_fmt = self._style._fmt
                self._style._fmt = original_fmt.replace("[ReqID:%(request_id)s] ", "")
                result = super().format(record)
                self._style._fmt = original_fmt
                return result
            elif not has_correlation_id:
                record.correlation_id = ""
                original_fmt = self._style._fmt
                self._style._fmt = original_fmt.replace(
                    "[CorrID:%(correlation_id)s] ", ""
                )
                result = super().format(record)
                self._style._fmt = original_fmt
                return result

            return super().format(record)

    formatter = RequestIdFormatter(
        "%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d]"
        + " [ReqID:%(request_id)s] [CorrID:%(correlation_id)s] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )

    # Create a console handler
    console_handler = logging.StreamHandler()
    console_handler.setLevel(log_level)
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # Create the logs directory if it doesn't exist
    today = datetime.datetime.now().strftime("%Y-%m-%d")
    log_dir = os.path.join("logs", today)
    os.makedirs(log_dir, exist_ok=True)

    # Create a timestamp for the log file
    timestamp = datetime.datetime.now().strftime("%H-%M-%S")
    log_file = os.path.join(log_dir, f"{logger_name}_{timestamp}.log")

    # Create a file handler
    file_handler = RotatingFileHandler(
        log_file, maxBytes=10 * 1024 * 1024, backupCount=5  # 10 MB
    )
    file_handler.setLevel(log_level)
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    # Add Kafka handler if requested
    if use_kafka:
        try:
            kafka_handler = KafkaLogHandler(
                bootstrap_servers=settings.kafka_bootstrap_servers,
                topic=kafka_topic,
                level=log_level,
            )
            kafka_handler.setFormatter(formatter)
            logger.addHandler(kafka_handler)
            logger.info(
                f"Kafka logging enabled for {logger_name}, sending to topic: {kafka_topic}"
            )
        except Exception as e:
            logger.error(f"Failed to initialize Kafka logging: {e}")

    # Log the logger setup
    logger.info(f"Logger {logger_name} configured with log file: {log_file}")

    # Wrap with RequestIdAdapter if requested
    if with_request_id:
        return RequestIdAdapter(logger, {})
    else:
        return logger

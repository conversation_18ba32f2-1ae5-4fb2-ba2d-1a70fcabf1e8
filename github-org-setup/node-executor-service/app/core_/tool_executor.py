"""
Tool Executor - A layer on top of components that handles component selection and execution.
"""

import json
import logging
import traceback
from typing import Dict, Any, Optional

from app.core_.component_system import COMPONENT_REGISTRY, get_component_manager

# Import the specialized tool executor logger
try:
    # First try to import the specialized Kafka logger for tool executor
    from app.utils.tool_executor_kafka_logger import setup_tool_executor_logger
    logger = setup_tool_executor_logger()
except ImportError:
    # If that fails, fall back to the standard logger
    try:
        from app.utils.logging_config import setup_logger
        logger = setup_logger("ToolExecutor")
    except ImportError:
        # Fallback to basic logging if logging_config is not available
        logging.basicConfig(
            level=logging.DEBUG,
            format="%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
        )
        logger = logging.getLogger(__name__)


def format_api_response(result: Dict[str, Any]) -> Dict[str, Any]:
    """
    Format API component response to match the desired structure.

    Args:
        result: The original API component result

    Returns:
        A formatted response with the desired structure
    """
    if not isinstance(result, dict):
        return result

    # Create a new response structure with the updated format
    formatted_response = {
        "result": result.get("data"),  # Move 'data' to 'result'
        "status_code": result.get("status_code"),
        "response_headers": result.get("headers", {}),  # Rename 'headers' to 'response_headers'
        "error": result.get("error")  # Include error field
    }

    # Keep URL and method for reference but not in the example
    if "url" in result:
        formatted_response["url"] = result["url"]
    if "method" in result:
        formatted_response["method"] = result["method"]

    return formatted_response


class ToolExecutor:
    """
    A layer on top of components that handles component selection and execution.

    This class is responsible for:
    1. Selecting the appropriate component based on the tool_name parameter
    2. Executing the component with the provided parameters
    3. Returning the result
    """

    def __init__(self):
        """
        Initialize the ToolExecutor.
        """
        logger.info("Initializing ToolExecutor")
        self.component_manager = get_component_manager()
        logger.info("ToolExecutor initialized successfully")

    async def execute_tool(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute a tool based on the tool_name parameter in the payload.

        Args:
            payload: The payload containing tool_name, tool_parameters, and request_id

        Returns:
            The result of the tool execution

        Raises:
            ValueError: If the tool_name is not provided or not found
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Executing tool for request_id: {request_id}")

        # Log the complete payload
        logger.info(f"ToolExecutor received payload: {json.dumps(payload, indent=2)}")

        # Extract tool_name from payload
        tool_name = payload.get("tool_name")
        if not tool_name:
            error_msg = f"tool_name is required in payload for request_id: {request_id}"
            logger.error(error_msg)
            error_result = {
                "status": "error",
                "error": error_msg
            }
            logger.info(f"ToolExecutor returning raw error result for missing tool_name: {request_id}")
            return error_result

        logger.info(f"Tool name: {tool_name} for request_id: {request_id}")

        # Extract tool_parameters from payload
        tool_parameters = payload.get("tool_parameters", {})
        logger.debug(f"Tool parameters for request_id {request_id}: {tool_parameters}")

        # Check if the component exists (case-insensitive lookup)
        actual_tool_name = self._find_component_name(tool_name)
        if actual_tool_name is None:
            error_msg = f"Component '{tool_name}' not found for request_id: {request_id}"
            logger.error(error_msg)
            logger.debug(f"Available components: {list(COMPONENT_REGISTRY.keys())}")
            error_result = {
                "status": "error",
                "error": error_msg
            }
            logger.info(f"ToolExecutor returning raw error result for missing component: {request_id}")
            return error_result

        # Use the actual component name found in registry
        tool_name = actual_tool_name

        try:
            # Get the component instance
            component = self.component_manager.get_component_instance(tool_name)
            logger.debug(
                f"Got component instance for {tool_name}: {component.__class__.__name__}"
            )

            # Create a new payload with the tool parameters
            component_payload = {**tool_parameters, "request_id": request_id}

            # Process the payload with the component
            logger.info(
                f"Processing payload with component {tool_name} for request_id: {request_id}"
            )
            result = await component.process(component_payload)
            logger.info(
                f"Component {tool_name} processed payload successfully for request_id: {request_id}"
            )

            # Return the raw component result without any wrapper metadata
            # Let ComponentSystem handle all response formatting and metadata addition
            logger.info(
                f"ToolExecutor returning raw component result for request_id: {request_id}"
            )
            logger.debug(f"Raw component result: {json.dumps(result, indent=2) if isinstance(result, dict) else str(result)}")

            # Return only the raw component result
            return result
        except Exception as e:
            error_msg = f"Error executing tool {tool_name} for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details: {traceback.format_exc()}")

            # Return raw error result without wrapper metadata
            # Let ComponentSystem handle error response formatting
            error_result = {
                "status": "error",
                "error": error_msg
            }

            logger.info(f"ToolExecutor returning raw error result for request_id: {request_id}")
            logger.debug(f"Raw error result: {json.dumps(error_result, indent=2)}")

            return error_result

    def _find_component_name(self, tool_name: str) -> str:
        """
        Find the actual component name in the registry using case-insensitive lookup.

        This handles cases where the orchestration engine sends 'InformationExtractor'
        but the component is registered as 'information_extractor'.

        Args:
            tool_name: The tool name to search for

        Returns:
            The actual component name if found, None otherwise
        """
        # First try exact match
        if tool_name in COMPONENT_REGISTRY:
            return tool_name

        # Try case-insensitive match
        tool_name_lower = tool_name.lower()
        for registered_name in COMPONENT_REGISTRY.keys():
            if registered_name.lower() == tool_name_lower:
                logger.info(f"Found case-insensitive match: '{tool_name}' -> '{registered_name}'")
                return registered_name

        # Try common case conversions
        # CamelCase -> snake_case (e.g., InformationExtractor -> information_extractor)
        snake_case_name = self._camel_to_snake(tool_name)
        if snake_case_name in COMPONENT_REGISTRY:
            logger.info(f"Found CamelCase->snake_case match: '{tool_name}' -> '{snake_case_name}'")
            return snake_case_name

        # snake_case -> CamelCase (e.g., information_extractor -> InformationExtractor)
        camel_case_name = self._snake_to_camel(tool_name)
        if camel_case_name in COMPONENT_REGISTRY:
            logger.info(f"Found snake_case->CamelCase match: '{tool_name}' -> '{camel_case_name}'")
            return camel_case_name

        return None

    def _camel_to_snake(self, name: str) -> str:
        """Convert CamelCase to snake_case."""
        import re
        # Insert underscore before uppercase letters that follow lowercase letters
        s1 = re.sub('(.)([A-Z][a-z]+)', r'\1_\2', name)
        # Insert underscore before uppercase letters that follow lowercase letters or digits
        return re.sub('([a-z0-9])([A-Z])', r'\1_\2', s1).lower()

    def _snake_to_camel(self, name: str) -> str:
        """Convert snake_case to CamelCase."""
        components = name.split('_')
        return ''.join(word.capitalize() for word in components)


# Create a global tool executor
tool_executor = None


def get_tool_executor() -> ToolExecutor:
    """
    Get or create the global tool executor.

    Returns:
        The tool executor
    """
    global tool_executor
    if tool_executor is None:
        logger.info("Creating new global ToolExecutor instance")
        tool_executor = ToolExecutor()
    logger.debug("Returning global ToolExecutor instance")
    return tool_executor


async def register_dynamic_tool(tool_name: str, process_function: callable) -> bool:
    """
    Dynamically register a new tool at runtime.

    This function creates a new component class and registers it with the component system,
    making it immediately available as a tool.

    Args:
        tool_name: The name of the tool to register
        process_function: The async function that will process the tool's payload
                          Should accept a dict and return a dict

    Returns:
        True if registration was successful, False otherwise
    """
    from app.core_.base_component import BaseComponent
    from app.core_.component_system import COMPONENT_REGISTRY

    logger.info(f"Dynamically registering new tool: {tool_name}")

    # Check if the tool already exists
    if tool_name in COMPONENT_REGISTRY:
        logger.warning(f"Tool {tool_name} already exists, cannot register")
        return False

    try:
        # Create a new component class
        class DynamicComponent(BaseComponent):
            """Dynamically created component."""

            def __init__(self):
                """Initialize the dynamic component."""
                logger.info(f"Initializing dynamic component: {tool_name}")
                super().__init__()
                logger.info(f"Dynamic component {tool_name} initialized successfully")

            async def process(self, payload):
                print(
                    "************************** \n**************************",
                    payload,
                    "************************** \n**************************",
                )
                """Process the payload using the provided function."""
                request_id = payload.get("request_id", "unknown")
                logger.info(
                    f"Processing dynamic component {tool_name} for request_id: {request_id}"
                )
                try:
                    result = await process_function(payload)
                    logger.info(
                        f"Dynamic component {tool_name} processed successfully for request_id: {request_id}"
                    )
                    return result
                except Exception as e:
                    error_msg = f"Error in dynamic component {tool_name}: {str(e)}"
                    logger.error(f"{error_msg} for request_id: {request_id}")
                    logger.debug(f"Exception details: {traceback.format_exc()}")
                    return {
                        "request_id": request_id,
                        "status": "error",
                        "result": {
                            "error": error_msg
                        }
                    }

        # Register the component
        DynamicComponent.component_type = tool_name
        COMPONENT_REGISTRY[tool_name] = DynamicComponent

        # Get the component manager and create an instance
        from app.core_.component_system import get_component_manager

        manager = get_component_manager()
        manager.components[tool_name] = DynamicComponent()

        logger.info(f"Successfully registered dynamic tool: {tool_name}")
        return True

    except Exception as e:
        logger.error(f"Failed to register dynamic tool {tool_name}: {str(e)}")
        logger.debug(f"Exception details: {traceback.format_exc()}")
        return False

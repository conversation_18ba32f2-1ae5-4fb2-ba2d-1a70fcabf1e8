"""
Document Component - Extracts content from documents (local files or URLs).
"""
import asyncio
import json
import logging
import os
import traceback
from typing import Dict, Any, Tuple, Optional, List
from urllib.parse import urlparse

from app.core_.base_component import BaseComponent, ValidationResult
from app.core_.component_system import register_component, get_component_manager
from app.config.config import settings


# Import the logger configuration
try:
    from app.utils.logging_config import setup_logger
    logger = setup_logger("DocComponent")
except ImportError:
    # Fallback to basic logging if logging_config is not available
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    logger = logging.getLogger(__name__)


@register_component("DocComponent")
class DocComponent(BaseComponent):
    """
    Component for extracting content from documents.
    """
    def __init__(self):
        """
        Initialize the Document component.
        """
        logger.info("Initializing Doc Component")

        try:
            super().__init__()
            logger.debug("Base component initialized")

            logger.info("Doc Component initialized successfully")

        except Exception as e:
            logger.error(f"Error initializing Doc Component: {str(e)}")
            logger.debug(f"Exception details: {traceback.format_exc()}")
            raise



    async def validate(self, payload: Dict[str, Any]) -> ValidationResult:
        """
        Validate a document extraction payload.

        Args:
            payload: The payload to validate

        Returns:
            ValidationResult with validation status
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Validating document extraction payload for request_id: {request_id}")
        logger.debug(f"Payload to validate for request_id {request_id}: {payload}")

        try:
            # Check for required fields - either file_path or url must be present
            has_file_path = "file_path" in payload and payload["file_path"]
            has_url = "url" in payload and payload["url"]

            if not has_file_path and not has_url:
                error_msg = f"Missing required field: either 'file_path' or 'url' must be provided for request_id {request_id}"
                logger.error(error_msg)
                return ValidationResult(
                    is_valid=False,
                    error_message=error_msg,
                    error_details={"input": "either file_path or url is required"}
                )

            # If both are provided, log a warning but proceed (prioritize file_path)
            if has_file_path and has_url:
                logger.warning(f"Both 'file_path' and 'url' provided for request_id {request_id}. Will prioritize file_path.")

            # Validate file_path if provided
            if has_file_path:
                file_path = payload["file_path"]
                logger.debug(f"Checking if file exists: {file_path} for request_id {request_id}")
                if not os.path.exists(file_path):
                    error_msg = f"File does not exist: {file_path} for request_id {request_id}"
                    logger.error(error_msg)
                    return ValidationResult(
                        is_valid=False,
                        error_message=error_msg,
                        error_details={"file_path": "file not found"}
                    )
                logger.debug(f"File exists: {file_path} for request_id {request_id}")

            # Validate URL if provided and no file_path
            if has_url and not has_file_path:
                url = payload["url"]
                # Basic URL validation
                try:
                    parsed_url = urlparse(url)
                    if not all([parsed_url.scheme, parsed_url.netloc]):
                        error_msg = f"Invalid URL format: {url} for request_id {request_id}"
                        logger.error(error_msg)
                        return ValidationResult(
                            is_valid=False,
                            error_message=error_msg,
                            error_details={"url": "invalid URL format"}
                        )

                    # Check if scheme is http or https
                    if parsed_url.scheme not in ["http", "https"]:
                        error_msg = f"URL scheme must be http or https: {url} for request_id {request_id}"
                        logger.error(error_msg)
                        return ValidationResult(
                            is_valid=False,
                            error_message=error_msg,
                            error_details={"url": "scheme must be http or https"}
                        )

                    logger.debug(f"URL format validation passed for {url}, request_id {request_id}")
                except Exception as e:
                    error_msg = f"URL validation error for {url}, request_id {request_id}: {str(e)}"
                    logger.error(error_msg)
                    return ValidationResult(
                        is_valid=False,
                        error_message=error_msg,
                        error_details={"url": f"validation error: {str(e)}"}
                    )

            # Validate file_type
            file_type = payload.get("file_type")
            if file_type is not None and file_type not in ["auto", "pdf", "docx", "text"]:
                error_msg = f"Invalid file_type: {file_type}. Must be one of 'auto', 'pdf', 'docx', 'text' for request_id {request_id}"
                logger.error(error_msg)
                return ValidationResult(
                    is_valid=False,
                    error_message=error_msg,
                    error_details={"file_type": "invalid value"}
                )
            logger.debug(f"file_type validation passed for request_id {request_id}")

            # Validate page_range if present
            page_range = payload.get("page_range")
            if page_range is not None:
                if not isinstance(page_range, list) or len(page_range) != 2 or not all(isinstance(i, int) and i >= 0 for i in page_range):
                    error_msg = f"Invalid page_range: {page_range}. Must be a list of two non-negative integers [start, end] for request_id {request_id}"
                    logger.error(error_msg)
                    return ValidationResult(
                        is_valid=False,
                        error_message=error_msg,
                        error_details={"page_range": "invalid format"}
                    )
                if page_range[0] > page_range[1]:
                    error_msg = f"Invalid page_range: {page_range}. Start page cannot be greater than end page for request_id {request_id}"
                    logger.error(error_msg)
                    return ValidationResult(
                        is_valid=False,
                        error_message=error_msg,
                        error_details={"page_range": "start > end"}
                    )
            logger.debug(f"page_range validation passed for request_id {request_id}")

            logger.info(f"Document extraction payload validation passed for request_id {request_id}")
            return ValidationResult(is_valid=True)

        except Exception as e:
            error_msg = f"Unexpected error during payload validation for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
            return ValidationResult(
                is_valid=False,
                error_message=error_msg,
                error_details={"validation_error": str(e)}
            )


    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Extract content from a document (file or URL).

        Args:
            payload: The request payload

        Returns:
            The extracted content and metadata
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Starting document extraction processing for request_id: {request_id}")
        logger.debug(f"Full payload for request_id {request_id}: {payload}")

        try:
            # Determine if we're processing a file path or URL
            has_file_path = "file_path" in payload and payload["file_path"]
            has_url = "url" in payload and payload["url"]

            # Get common parameters
            file_type = payload.get("file_type", "auto")
            max_content_length = payload.get("max_content_length", settings.doc_default_max_content_length)
            extract_metadata = payload.get("extract_metadata", False)
            page_range = payload.get("page_range")

            # Process based on input type (file path or URL)
            if has_file_path:
                # Process local file
                file_path = os.path.abspath(os.path.normpath(payload["file_path"]))
                logger.info(f"Extracting document from file: Path={file_path}, Type={file_type}, MaxLength={max_content_length}, ExtractMetadata={extract_metadata}, PageRange={page_range} for request_id {request_id}")

                return await self._process_file(
                    file_path=file_path,
                    file_type=file_type,
                    max_content_length=max_content_length,
                    extract_metadata=extract_metadata,
                    page_range=page_range,
                    request_id=request_id
                )

            elif has_url:
                # Process URL
                url = payload["url"]
                logger.info(f"Extracting document from URL: URL={url}, Type={file_type}, MaxLength={max_content_length}, ExtractMetadata={extract_metadata}, PageRange={page_range} for request_id {request_id}")

                return await self._process_url(
                    url=url,
                    file_type=file_type,
                    max_content_length=max_content_length,
                    extract_metadata=extract_metadata,
                    page_range=page_range,
                    request_id=request_id
                )

            else:
                # This should be caught by validation, but included for safety
                error_msg = f"Neither file_path nor url provided for request_id {request_id}"
                logger.error(error_msg)
                return {
                    "status": "error",
                    "error": error_msg,
                    "content": None,
                    "metadata": {}
                }

        except Exception as e:
            error_msg = f"Unexpected error during document extraction for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
            return {
                "status": "error",
                "error": error_msg,
                "content": None,
                "metadata": {}
            }

    async def _process_file(self, file_path: str, file_type: str, max_content_length: int,
                           extract_metadata: bool, page_range: Optional[List[int]],
                           request_id: str) -> Dict[str, Any]:
        """
        Process a document from a local file.

        Args:
            file_path: Path to the file
            file_type: Type of file to extract
            max_content_length: Maximum content length to extract
            extract_metadata: Whether to extract metadata
            page_range: Range of pages to extract
            request_id: Request ID for logging

        Returns:
            The extracted content and metadata
        """
        try:
            # Auto-detect file type if needed
            if file_type == "auto":
                file_type = self._detect_file_type(file_path)
                logger.info(f"Auto-detected file type: {file_type} for {file_path}, request_id {request_id}")

            # Extract content based on file type
            content = None
            metadata = {}

            if file_type == "pdf":
                logger.debug(f"Calling _extract_pdf for {file_path}, request_id {request_id}")
                content, metadata = await self._extract_pdf(
                    file_path, max_content_length, extract_metadata, page_range
                )
            elif file_type == "docx":
                logger.debug(f"Calling _extract_docx for {file_path}, request_id {request_id}")
                content, metadata = await self._extract_docx(
                    file_path, max_content_length, extract_metadata
                )
            elif file_type == "text":
                logger.debug(f"Calling _extract_text for {file_path}, request_id {request_id}")
                content, metadata = await self._extract_text(
                    file_path, max_content_length, extract_metadata
                )
            else:
                # This case should be caught by validation, but included for safety
                error_msg = f"Unsupported file type for extraction: {file_type} for request_id {request_id}"
                logger.error(error_msg)
                return {
                    "status": "error",
                    "error": error_msg,
                    "content": None,
                    "metadata": {}
                }

            logger.info(f"Document extraction completed for {file_path}, request_id {request_id}")
            logger.debug(f"Extracted content (truncated for log) for request_id {request_id}: {str(content)[:200]}...")
            logger.debug(f"Extracted metadata for request_id {request_id}: {metadata}")

            return {
                "status": "success",
                "content": content,
                "metadata": metadata
            }

        except FileNotFoundError:
            error_msg = f"File not found during extraction: {file_path} for request_id {request_id}"
            logger.error(error_msg)
            return {
                "status": "error",
                "error": error_msg,
                "content": None,
                "metadata": {}
            }
        except Exception as e:
            error_msg = f"Error during document extraction from {file_path} for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
            return {
                "status": "error",
                "error": error_msg,
                "content": None,
                "metadata": {}
            }

    async def _process_url(self, url: str, file_type: str, max_content_length: int,
                          extract_metadata: bool, page_range: Optional[List[int]],
                          request_id: str) -> Dict[str, Any]:
        """
        Process a document from a URL.

        Args:
            url: URL to fetch the document from
            file_type: Type of file to extract
            max_content_length: Maximum content length to extract
            extract_metadata: Whether to extract metadata
            page_range: Range of pages to extract
            request_id: Request ID for logging

        Returns:
            The extracted content and metadata
        """
        try:
            # Try to get the API component to fetch the URL
            try:
                # First try to get it from the component manager
                component_manager = get_component_manager()
                try:
                    api_component = component_manager.get_component_instance("ApiRequestNode")
                except ValueError:
                    # If not registered, import and create it directly
                    logger.info(f"ApiRequestNode not registered, creating it directly for request_id {request_id}")
                    from app.components.api_component import ApiComponent
                    api_component = ApiComponent()

                if not api_component:
                    error_msg = f"Could not create ApiComponent for request_id {request_id}"
                    logger.error(error_msg)
                    return {
                        "status": "error",
                        "error": error_msg,
                        "content": None,
                        "metadata": {}
                    }
            except Exception as e:
                error_msg = f"Error getting ApiComponent for request_id {request_id}: {str(e)}"
                logger.error(error_msg)
                return {
                    "status": "error",
                    "error": error_msg,
                    "content": None,
                    "metadata": {}
                }

            # Prepare API request payload
            api_payload = {
                "request_id": request_id,
                "url": url,
                "method": "GET",
                "max_content_length": max_content_length
            }

            # Execute API request
            logger.info(f"Fetching document from URL: {url} for request_id {request_id}")
            api_result = await api_component.process(api_payload)

            # Check for API errors
            if api_result.get("error") or api_result.get("status_code", 0) >= 400:
                error_msg = f"Error fetching URL {url} for request_id {request_id}: {api_result.get('error', f'Status code: {api_result.get('status_code')}')})"
                logger.error(error_msg)
                return {
                    "status": "error",
                    "error": error_msg,
                    "content": None,
                    "metadata": {}
                }

            # Get content from API response
            response_data = api_result.get("data")
            if response_data is None:
                error_msg = f"No data returned from URL {url} for request_id {request_id}"
                logger.error(error_msg)
                return {
                    "status": "error",
                    "error": error_msg,
                    "content": None,
                    "metadata": {}
                }

            # Get content type from response headers
            content_type = api_result.get("content_type", "")

            # Auto-detect file type if needed
            if file_type == "auto":
                file_type = self._detect_content_type(content_type, url)
                logger.info(f"Auto-detected file type from URL: {file_type} for {url}, request_id {request_id}")

            # Process content based on file type
            content = None
            metadata = {}

            if file_type == "pdf":
                # For PDF, we would need to save the content to a temporary file and process it
                # This is a simplified implementation
                logger.warning(f"PDF processing from URL not fully implemented for {url}, request_id {request_id}")
                content = {
                    "text": f"[PDF Content from {url}]",
                    "pages": 1
                }

                if extract_metadata:
                    metadata = {
                        "title": os.path.basename(url),
                        "source_url": url,
                        "content_type": content_type,
                        "content_length": len(str(response_data)) if isinstance(response_data, str) else 0
                    }

            elif file_type == "docx":
                # For DOCX, we would need to save the content to a temporary file and process it
                # This is a simplified implementation
                logger.warning(f"DOCX processing from URL not fully implemented for {url}, request_id {request_id}")
                content = {
                    "text": f"[DOCX Content from {url}]",
                    "paragraphs": 1
                }

                if extract_metadata:
                    metadata = {
                        "title": os.path.basename(url),
                        "source_url": url,
                        "content_type": content_type,
                        "content_length": len(str(response_data)) if isinstance(response_data, str) else 0
                    }

            elif file_type == "text":
                # For text, we can process the response directly
                if isinstance(response_data, str):
                    text_content = response_data
                elif isinstance(response_data, dict) or isinstance(response_data, list):
                    # If it's JSON, convert to string
                    text_content = json.dumps(response_data)
                else:
                    text_content = str(response_data)

                # Truncate if needed
                if max_content_length > 0 and len(text_content) > max_content_length:
                    text_content = text_content[:max_content_length]
                    logger.warning(f"Content from URL {url} truncated to {max_content_length} characters for request_id {request_id}")

                content = {
                    "text": text_content,
                    "length": len(text_content)
                }

                if extract_metadata:
                    metadata = {
                        "source_url": url,
                        "content_type": content_type,
                        "content_length": len(text_content),
                        "headers": api_result.get("headers", {})
                    }

            else:
                # This case should be caught by validation, but included for safety
                error_msg = f"Unsupported file type for URL extraction: {file_type} for request_id {request_id}"
                logger.error(error_msg)
                return {
                    "status": "error",
                    "error": error_msg,
                    "content": None,
                    "metadata": {}
                }

            logger.info(f"Document extraction from URL completed for {url}, request_id {request_id}")
            logger.debug(f"Extracted content (truncated for log) for request_id {request_id}: {str(content)[:200]}...")
            logger.debug(f"Extracted metadata for request_id {request_id}: {metadata}")

            return {
                "status": "success",
                "content": content,
                "metadata": metadata
            }

        except Exception as e:
            error_msg = f"Error during document extraction from URL {url} for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
            return {
                "status": "error",
                "error": error_msg,
                "content": None,
                "metadata": {}
            }


    def _detect_file_type(self, file_path: str) -> str:
        """
        Detect the file type based on extension.

        Args:
            file_path: Path to the file

        Returns:
            The detected file type
        """
        logger.debug(f"Detecting file type for {file_path}")
        _, ext = os.path.splitext(file_path)
        ext = ext.lower().lstrip(".")

        if ext == "pdf":
            logger.debug(f"Detected type 'pdf' for {file_path}")
            return "pdf"
        elif ext == "docx":
            logger.debug(f"Detected type 'docx' for {file_path}")
            return "docx"
        else:
            logger.debug(f"Detected type 'text' (default) for {file_path}")
            return "text"

    def _detect_content_type(self, content_type: str, url: str) -> str:
        """
        Detect the content type based on the HTTP Content-Type header and URL.

        Args:
            content_type: The Content-Type header from the HTTP response
            url: The URL of the document

        Returns:
            The detected file type (pdf, docx, or text)
        """
        logger.debug(f"Detecting content type from Content-Type: {content_type} and URL: {url}")

        # Check Content-Type header first
        content_type = content_type.lower()

        if "application/pdf" in content_type:
            logger.debug(f"Detected type 'pdf' from Content-Type for {url}")
            return "pdf"
        elif "application/vnd.openxmlformats-officedocument.wordprocessingml.document" in content_type:
            logger.debug(f"Detected type 'docx' from Content-Type for {url}")
            return "docx"
        elif "text/" in content_type or "application/json" in content_type:
            logger.debug(f"Detected type 'text' from Content-Type for {url}")
            return "text"

        # If Content-Type is not conclusive, check URL extension
        _, ext = os.path.splitext(url)
        ext = ext.lower().lstrip(".")

        if ext == "pdf":
            logger.debug(f"Detected type 'pdf' from URL extension for {url}")
            return "pdf"
        elif ext == "docx":
            logger.debug(f"Detected type 'docx' from URL extension for {url}")
            return "docx"

        # Default to text for all other cases
        logger.debug(f"Defaulting to type 'text' for {url}")
        return "text"

    async def _extract_pdf(self, file_path: str, max_length: int, extract_metadata: bool, page_range=None) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """Extract content from a PDF file."""
        logger.info(f"Extracting PDF: {file_path}")
        # Simulate processing time
        await asyncio.sleep(0.5)

        # This is a placeholder implementation
        content = {
            "text": f"[PDF Content from {file_path}]",
            "pages": 10
        }
        logger.debug(f"Simulated PDF content extraction for {file_path}")

        metadata = {}
        if extract_metadata:
             metadata = {
                 "title": os.path.basename(file_path),
                 "author": "Unknown",
                 "creation_date": "Unknown",
                 "file_size": os.path.getsize(file_path) if os.path.exists(file_path) else 0
             }
             logger.debug(f"Simulated PDF metadata extraction for {file_path}")

        logger.info(f"PDF extraction simulated for {file_path}")
        return content, metadata

    async def _extract_docx(self, file_path: str, max_length: int, extract_metadata: bool) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """Extract content from a DOCX file."""
        logger.info(f"Extracting DOCX: {file_path}")
        # Simulate processing time
        await asyncio.sleep(0.3)

        # This is a placeholder implementation
        content = {
            "text": f"[DOCX Content from {file_path}]",
            "paragraphs": 20
        }
        logger.debug(f"Simulated DOCX content extraction for {file_path}")

        metadata = {}
        if extract_metadata:
             metadata = {
                 "title": os.path.basename(file_path),
                 "author": "Unknown",
                 "creation_date": "Unknown",
                 "file_size": os.path.getsize(file_path) if os.path.exists(file_path) else 0
             }
             logger.debug(f"Simulated DOCX metadata extraction for {file_path}")

        logger.info(f"DOCX extraction simulated for {file_path}")
        return content, metadata

    async def _extract_text(self, file_path: str, max_length: int, extract_metadata: bool) -> Tuple[Dict[str, Any], Dict[str, Any]]:
        """Extract content from a text file."""
        logger.info(f"Extracting Text: {file_path} with max_length {max_length}")
        # Simulate processing time
        await asyncio.sleep(0.1)

        text = ""
        try:
            logger.debug(f"Reading text file: {file_path}")
            # Check file size before reading (keeping this check for functionality)
            file_size = os.path.getsize(file_path) if os.path.exists(file_path) else 0
            if file_size > max_length > 0: # Only apply limit if max_length > 0
                 logger.warning(f"File size {file_size} exceeds max_length {max_length}. Content will be truncated.")

            with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                # Read up to max_length characters
                text = f.read(max_length)
            logger.debug(f"Successfully read {len(text)} characters from {file_path}")


        except FileNotFoundError:
             logger.error(f"Text file not found during extraction: {file_path}")
             text = f"[ERROR READING FILE: File not found at {file_path}]"
        except Exception as e:
            logger.error(f"Error reading text file {file_path}: {e}")
            logger.debug(f"Exception details: {traceback.format_exc()}")
            text = f"[ERROR READING FILE: {str(e)}]"

        content = {
            "text": text,
            "length": len(text)
        }
        logger.debug(f"Created text content object for {file_path}")

        metadata = {}
        if extract_metadata:
             try:
                 metadata = {
                     "filename": os.path.basename(file_path),
                     "file_size": os.path.getsize(file_path) if os.path.exists(file_path) else 0,
                     "last_modified": os.path.getmtime(file_path) if os.path.exists(file_path) else None
                 }
                 logger.debug(f"Extracted text metadata for {file_path}")
             except Exception as e:
                 logger.warning(f"Could not get metadata for {file_path}: {e}")
                 metadata = {"error": f"Could not get metadata: {str(e)}"}


        logger.info(f"Text extraction completed for {file_path}")
        return content, metadata

"""
Conditional Component - Evaluates conditions and returns routing decisions.

This component executes as a separate workflow step and returns the target transition
for the orchestration engine to follow. It implements the new component-based
conditional routing architecture, replacing the previous embedded routing pattern.

Key Features:
- Evaluates multiple conditions sequentially (first match wins)
- Supports both node_output and global_context data sources
- Implements 9 comparison operators for flexible condition logic
- Returns structured routing decisions for orchestration engine
- Provides comprehensive error handling and logging

"""

from typing import Dict, Any, List, Optional
import logging
from pydantic import BaseModel, Field, validator
from app.core_.base_component import BaseComponent
from app.core_.component_system import register_component

logger = logging.getLogger(__name__)


class ConditionSchema(BaseModel):
    """Schema for individual condition validation."""

    source: str = Field(..., description="Source of data: node_output or global_context")
    operator: str = Field(..., description="Comparison operator")
    expected_value: Any = Field(..., description="Expected value for comparison")
    variable_name: Optional[str] = Field(None, description="Variable name for global_context source")
    next_transition: str = Field(..., description="Target transition if condition matches")

    @validator('source')
    def validate_source(cls, v):
        """Validate that source is either node_output or global_context."""
        if v not in ['node_output', 'global_context']:
            raise ValueError('source must be either "node_output" or "global_context"')
        return v

    @validator('operator')
    def validate_operator(cls, v):
        """Validate that operator is one of the supported operators."""
        valid_operators = [
            'equals', 'not_equals', 'contains', 'starts_with', 'ends_with',
            'greater_than', 'less_than', 'exists', 'is_empty'
        ]
        if v not in valid_operators:
            raise ValueError(f'operator must be one of: {", ".join(valid_operators)}')
        return v

    @validator('variable_name', always=True)
    def validate_variable_name_for_global_context(cls, v, values):
        """Validate that variable_name is provided when source is global_context."""
        if values.get('source') == 'global_context' and not v:
            raise ValueError('variable_name is required when source is "global_context"')
        return v


class ConditionalRequestSchema(BaseModel):
    """Schema for conditional component request validation."""

    request_id: str = Field(..., description="Unique request identifier")
    conditions: List[ConditionSchema] = Field(..., description="List of conditions to evaluate")
    global_context: Dict[str, Any] = Field(default_factory=dict, description="Global context variables")
    node_output: Any = Field(..., description="Output from previous node")
    default_transition: str = Field(..., description="Default transition if no conditions match")
    allow_multiple_matches: bool = Field(default=False, description="Allow multiple conditions to match simultaneously")
    evaluation_strategy: str = Field(default="first_match", description="Strategy: 'first_match' or 'all_matches'")

    @validator('conditions')
    def validate_conditions_not_empty(cls, v):
        """Validate that conditions list is not empty."""
        if not v:
            raise ValueError('conditions list cannot be empty')
        return v

    @validator('evaluation_strategy')
    def validate_evaluation_strategy(cls, v):
        """Validate that evaluation strategy is supported."""
        if v not in ['first_match', 'all_matches']:
            raise ValueError('evaluation_strategy must be either "first_match" or "all_matches"')
        return v


@register_component("conditional")
class ConditionalComponent(BaseComponent):
    """
    Conditional routing component that evaluates conditions and returns routing decisions.
    
    This component executes as a separate workflow step and returns the target transition
    for the orchestration engine to follow.
    
    Architecture:
    - Inherits from BaseComponent following standard component pattern
    - Registered with @register_component decorator for discovery
    - Implements async process() method for condition evaluation
    - Returns routing decisions in standardized format
    """
    
    def __init__(self):
        """
        Initialize the ConditionalComponent.

        Sets up the component with proper inheritance and component type.
        """
        super().__init__()
        self.component_type = "conditional"
        self.request_schema = ConditionalRequestSchema
        logger.info("ConditionalComponent initialized successfully")

    async def validate_input(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Validate input payload against the ConditionalRequestSchema.

        Args:
            payload: Input payload to validate

        Returns:
            Dict containing validation result:
            - is_valid: Boolean indicating if validation passed
            - error_message: String describing validation errors (if any)
            - validated_data: Parsed and validated data (if validation passed)
        """
        try:
            # Attempt to validate the payload using Pydantic schema
            validated_data = self.request_schema(**payload)

            logger.debug(f"Input validation passed for request_id: {payload.get('request_id', 'unknown')}")

            return {
                "is_valid": True,
                "error_message": None,
                "validated_data": validated_data.dict()
            }

        except Exception as e:
            error_message = str(e)
            logger.warning(f"Input validation failed: {error_message}")

            return {
                "is_valid": False,
                "error_message": error_message,
                "validated_data": None
            }
    
    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process conditional routing logic.

        This is the main entry point for conditional evaluation. Integrates validation,
        condition evaluation, and routing decision logic.

        Args:
            payload: Contains conditions, global context, and previous node output

        Returns:
            Dict containing routing decision and metadata
        """
        import time
        start_time = time.time()

        try:
            request_id = payload.get("request_id", "unknown")
            logger.info(f"Processing conditional routing for request_id: {request_id}")

            # Step 1: Validate input payload
            validation_result = await self.validate_input(payload)
            if not validation_result["is_valid"]:
                execution_time = (time.time() - start_time) * 1000
                logger.warning(f"Input validation failed: {validation_result['error_message']}")

                return {
                    "status": "error",
                    "error": validation_result["error_message"],
                    "routing_decision": {
                        "target_transition": payload.get("default_transition", "default"),
                        "matched_condition": None,
                        "condition_result": False,
                        "execution_time_ms": execution_time
                    },
                    "metadata": {
                        "total_conditions": 0,
                        "evaluation_order": None
                    },
                    "input_data": payload.get("node_output")  # Include original input data for data flow
                }

            # Step 2: Extract validated data
            validated_data = validation_result["validated_data"]
            conditions = validated_data["conditions"]
            global_context = validated_data["global_context"]
            node_output = validated_data["node_output"]
            default_transition = validated_data["default_transition"]
            allow_multiple_matches = validated_data.get("allow_multiple_matches", False)
            evaluation_strategy = validated_data.get("evaluation_strategy", "first_match")

            # Step 3: Evaluate conditions based on strategy
            if evaluation_strategy == "first_match":
                # Original behavior: first match wins
                return await self._evaluate_first_match(conditions, global_context, node_output, default_transition, start_time)
            elif evaluation_strategy == "all_matches":
                # New behavior: evaluate all conditions and return multiple matches
                return await self._evaluate_all_matches(conditions, global_context, node_output, default_transition, start_time)

            # Fallback - should not reach here if strategies are implemented correctly
            logger.error(f"Unknown evaluation strategy: {evaluation_strategy}")
            execution_time = (time.time() - start_time) * 1000

            return {
                "status": "error",
                "error": f"Unknown evaluation strategy: {evaluation_strategy}",
                "routing_decision": {
                    "target_transition": default_transition,
                    "matched_condition": None,
                    "condition_result": False,
                    "execution_time_ms": execution_time
                },
                "metadata": {
                    "total_conditions": len(conditions),
                    "evaluation_order": None
                },
                "input_data": node_output  # Include original input data for data flow
            }

        except Exception as e:
            execution_time = (time.time() - start_time) * 1000
            logger.error(f"Error in conditional routing: {e}", exc_info=True)

            return {
                "status": "error",
                "error": str(e),
                "routing_decision": {
                    "target_transitions": [payload.get("default_transition", "default")],  # Array format for consistency
                    "matched_conditions": [],
                    "condition_result": False,
                    "execution_time_ms": execution_time
                },
                "metadata": {
                    "total_conditions": 0,
                    "total_matches": 0,
                    "evaluation_strategy": payload.get("evaluation_strategy", "first_match")
                },
                "input_data": payload.get("node_output")  # Include original input data for data flow
            }

    async def _evaluate_condition(
        self,
        condition: Dict[str, Any],
        global_context: Dict[str, Any],
        node_output: Any
    ) -> bool:
        """
        Evaluate a single condition.

        Args:
            condition: Condition configuration dict
            global_context: Global context variables
            node_output: Output from previous node

        Returns:
            Boolean result of condition evaluation
        """
        try:
            source = condition.get("source", "node_output")
            operator = condition.get("operator", "equals")
            expected_value = condition.get("expected_value")
            variable_name = condition.get("variable_name")

            # Get actual value based on source
            if source == "node_output":
                actual_value = node_output
            elif source == "global_context":
                actual_value = global_context.get(variable_name)
            else:
                logger.warning(f"Unknown condition source: {source}")
                return False

            # Apply operator
            return self._apply_operator(operator, actual_value, expected_value)

        except Exception as e:
            logger.error(f"Error evaluating condition: {e}")
            return False

    def _apply_operator(self, operator: str, actual_value: Any, expected_value: Any) -> bool:
        """
        Apply comparison operator to values.

        Args:
            operator: Comparison operator string
            actual_value: Actual value from data source
            expected_value: Expected value for comparison

        Returns:
            Boolean result of operator application
        """
        try:
            if operator == "equals":
                return actual_value == expected_value
            elif operator == "not_equals":
                return actual_value != expected_value
            elif operator == "contains":
                return str(expected_value) in str(actual_value) if actual_value is not None else False
            elif operator == "starts_with":
                return str(actual_value).startswith(str(expected_value)) if actual_value is not None else False
            elif operator == "ends_with":
                return str(actual_value).endswith(str(expected_value)) if actual_value is not None else False
            elif operator == "greater_than":
                return float(actual_value) > float(expected_value)
            elif operator == "less_than":
                return float(actual_value) < float(expected_value)
            elif operator == "exists":
                return actual_value is not None
            elif operator == "is_empty":
                return actual_value is None or actual_value == "" or actual_value == [] or actual_value == {}
            else:
                logger.warning(f"Unknown operator: {operator}")
                return False
        except Exception as e:
            logger.error(f"Error applying operator {operator}: {e}")
            return False

    async def _evaluate_first_match(
        self,
        conditions: List[Dict[str, Any]],
        global_context: Dict[str, Any],
        node_output: Any,
        default_transition: str,
        start_time: float
    ) -> Dict[str, Any]:
        """
        Evaluate conditions with first-match strategy (original behavior).

        Args:
            conditions: List of condition configurations
            global_context: Global context variables
            node_output: Output from previous node
            default_transition: Default transition if no conditions match
            start_time: Start time for execution timing

        Returns:
            Routing decision with single target transition
        """
        import time

        # Evaluate conditions sequentially (first match wins)
        for i, condition in enumerate(conditions):
            condition_matched = await self._evaluate_condition(condition, global_context, node_output)

            if condition_matched:
                target_transition = condition.get("next_transition")
                execution_time = (time.time() - start_time) * 1000

                logger.info(f"Condition {i+1} matched, routing to: {target_transition}")

                return {
                    "status": "success",
                    "routing_decision": {
                        "target_transition": target_transition,
                        "matched_condition": i + 1,
                        "condition_result": True,
                        "execution_time_ms": execution_time
                    },
                    "metadata": {
                        "total_conditions": len(conditions),
                        "evaluation_order": i + 1
                    },
                    "input_data": node_output  # Include original input data for data flow
                }

        # No conditions matched, use default transition
        execution_time = (time.time() - start_time) * 1000
        logger.info(f"No conditions matched, using default: {default_transition}")

        return {
            "status": "success",
            "routing_decision": {
                "target_transition": default_transition,
                "matched_condition": None,
                "condition_result": False,
                "execution_time_ms": execution_time
            },
            "metadata": {
                "total_conditions": len(conditions),
                "evaluation_order": None
            },
            "input_data": node_output  # Include original input data for data flow
        }

    async def _evaluate_all_matches(
        self,
        conditions: List[Dict[str, Any]],
        global_context: Dict[str, Any],
        node_output: Any,
        default_transition: str,
        start_time: float
    ) -> Dict[str, Any]:
        """
        Evaluate conditions with all-matches strategy (new behavior for multiple transitions).

        Args:
            conditions: List of condition configurations
            global_context: Global context variables
            node_output: Output from previous node
            default_transition: Default transition if no conditions match
            start_time: Start time for execution timing

        Returns:
            Routing decision with multiple target transitions
        """
        import time

        matched_transitions = []
        matched_conditions = []

        # Evaluate all conditions and collect matches
        for i, condition in enumerate(conditions):
            condition_matched = await self._evaluate_condition(condition, global_context, node_output)

            if condition_matched:
                target_transition = condition.get("next_transition")
                matched_transitions.append(target_transition)
                matched_conditions.append(i + 1)
                logger.info(f"Condition {i+1} matched, will route to: {target_transition}")

        execution_time = (time.time() - start_time) * 1000

        # If conditions matched, return multiple transitions
        if matched_transitions:
            logger.info(f"Multiple conditions matched ({len(matched_transitions)}), routing to: {matched_transitions}")

            return {
                "status": "success",
                "routing_decision": {
                    "target_transitions": matched_transitions,  # Array of transitions
                    "matched_conditions": matched_conditions,
                    "condition_result": True,
                    "execution_time_ms": execution_time
                },
                "metadata": {
                    "total_conditions": len(conditions),
                    "total_matches": len(matched_transitions),
                    "evaluation_strategy": "all_matches"
                },
                "input_data": node_output  # Include original input data for data flow
            }

        # No conditions matched, use default transition
        logger.info(f"No conditions matched, using default: {default_transition}")

        return {
            "status": "success",
            "routing_decision": {
                "target_transitions": [default_transition],  # Array format for consistency
                "matched_conditions": [],
                "condition_result": False,
                "execution_time_ms": execution_time
            },
            "metadata": {
                "total_conditions": len(conditions),
                "total_matches": 0,
                "evaluation_strategy": "all_matches"
            },
            "input_data": node_output  # Include original input data for data flow
        }

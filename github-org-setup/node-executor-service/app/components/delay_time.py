import logging
import traceback
import asyncio
from typing import Dict, Any, Optional

from pydantic import BaseModel, Field, field_validator

from app.core_.base_component import BaseComponent, ValidationResult
from app.core_.component_system import register_component

logger = logging.getLogger(__name__)

# The Pydantic schema is already correct for the desired functionality.
class DelayRequest(BaseModel):
    delay_seconds: float = Field(...)
    input_data: Optional[Any] = Field(None)
    request_id: Optional[str] = Field(None)
    # ... validators ...

@register_component("DelayComponent")
class DelayComponentExecutor(BaseComponent):
    # ... __init__ and validate methods are correct ...

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        request_id = payload.get("request_id", "unknown")
        
        try:
            # Type coercion and validation logic is correct.
            raw_delay_value = payload.get('delay_seconds')
            if raw_delay_value is None:
                raise ValueError("Required field 'delay_seconds' is missing.")
            
            delay_duration = float(raw_delay_value)

            if not (0 <= delay_duration <= 3600):
                raise ValueError("Delay must be between 0 and 3600 seconds.")

            logger.info(f"Starting delay of {delay_duration} seconds for request_id: {request_id}")
            await asyncio.sleep(delay_duration)
            logger.info(f"Delay of {delay_duration} seconds completed for request_id: {request_id}")
            
            # This logic is correct. It gets the data to pass through.
            passthrough_data = payload.get("input_data")

            # --- FIX #2: MATCH OUTPUT HANDLE NAME (CASE-SENSITIVE) ---
            # The key in this dictionary must exactly match the `name` in the
            # frontend Output() definition. 'Message' with a capital 'M'.
            return {
                "status": "success",
                "output": passthrough_data,
                "Message": f"Successfully waited for {delay_duration} seconds."
            }
            # --- END OF FIX #2 ---

        except ValueError as ve:
            # ... error handling is correct ...
            return {"status": "error", "error": f"Invalid input: {ve}"}
        except Exception as e:
            # ... error handling is correct ...
            return {"status": "error", "error": "An unexpected internal error occurred."}
"""
Convert Script Data Component for Node Executor Service

This component converts script generator output format to a metadata dictionary
that can be processed by AlterMetadataComponent.
"""
import copy
import logging
from typing import Any, Dict, List, Optional

from pydantic import BaseModel, Field, field_validator

from app.core_.base_component import BaseComponent, ValidationResult
from app.core_.component_system import register_component

logger = logging.getLogger(__name__)


# Define request schema using Pydantic
class ConvertScriptDataRequest(BaseModel):
    """Schema for script data conversion requests."""
    script_data: List[Dict[str, Any]] = Field(..., description="Array of script data objects from script generator")

    @field_validator('script_data')
    def validate_script_data(cls, v):
        if not isinstance(v, list):
            raise ValueError(f"script_data must be a list, got {type(v).__name__}")
        return v


@register_component("ConvertScriptDataComponent")
class ConvertScriptDataComponent(BaseComponent):
    """
    Converts script generator output format to metadata dictionary.
    
    Takes an array of objects with property_name and data fields and converts
    them to a flat dictionary structure suitable for metadata processing.
    """
    
    request_schema = ConvertScriptDataRequest

    async def validate(self, payload: Dict[str, Any]) -> ValidationResult:
        """
        Validate the input payload for script data conversion.

        Args:
            payload: The input payload containing script data

        Returns:
            ValidationResult indicating if the payload is valid
        """
        try:
            # Extract parameters from payload
            if "tool_parameters" in payload:
                parameters = payload["tool_parameters"]
            else:
                parameters = payload

            # Validate using Pydantic schema
            self.request_schema(**parameters)
            
            logger.info(f"Validation successful for ConvertScriptDataComponent")
            return ValidationResult(is_valid=True)

        except Exception as e:
            error_msg = f"Validation failed for ConvertScriptDataComponent: {str(e)}"
            logger.error(error_msg)
            return ValidationResult(
                is_valid=False,
                error_message=error_msg,
                error_details={"validation_error": str(e)}
            )

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process the script data conversion request.

        Args:
            payload: The input payload containing script data

        Returns:
            Dictionary containing the conversion results or error information
        """
        request_id = payload.get("request_id", "unknown")
        
        try:
            # Extract parameters from payload
            if "tool_parameters" in payload:
                parameters = payload["tool_parameters"]
            else:
                parameters = payload

            # Get inputs from parameters
            script_data = parameters.get("script_data", [])

            logger.info(f"Converting script data for request_id {request_id}")
            logger.debug(f"Script data items: {len(script_data)}")

            # Convert script data to metadata dictionary
            metadata_dict = {}
            
            for item in script_data:
                if not isinstance(item, dict):
                    logger.warning(f"Skipping non-dictionary item: {item}")
                    continue
                
                property_name = item.get("property_name")
                data = item.get("data")
                
                if property_name and data is not None:
                    metadata_dict[property_name] = data
                    logger.debug(f"Added property: {property_name}")

            logger.info(f"Script data converted successfully for request_id {request_id}. Properties: {list(metadata_dict.keys())}")

            return {
                "status": "success",
                "result": metadata_dict
            }

        except Exception as e:
            error_msg = f"Error converting script data for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            return {
                "status": "error",
                "error": error_msg,
                "error_details": {
                    "request_id": request_id,
                    "error_type": type(e).__name__,
                    "error_message": str(e)
                }
            }

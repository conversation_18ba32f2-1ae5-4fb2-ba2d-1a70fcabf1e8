"""
Split Text Component - Splits text into a list using a delimiter.
"""
import logging
import re
import traceback # Import traceback
from typing import Dict, Any

from app.core_.base_component import BaseComponent, ValidationResult # Import ValidationResult
from app.core_.component_system import register_component
from app.config.config import settings # Import settings

# Import the logger configuration
try:
    from app.utils.logging_config import setup_logger
    logger = setup_logger("SplitTextComponent")
except ImportError:
    # Fallback to basic logging if logging_config is not available
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    logger = logging.getLogger(__name__)


@register_component("SplitTextComponent")
class SplitTextComponent(BaseComponent):
    """
    Component for splitting text into a list using a delimiter.
    """
    def __init__(self):
        """
        Initialize the Split Text component.
        """
        logger.info("Initializing Split Text Component")
        super().__init__()
        logger.info("Split Text Component initialized successfully")


    async def validate(self, payload: Dict[str, Any]) -> ValidationResult: # Change return type hint
        """
        Validate a split text payload.

        Args:
            payload: The payload to validate

        Returns:
            ValidationResult with validation status
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Validating split text payload for request_id: {request_id}")
        logger.debug(f"Payload to validate for request_id {request_id}: {payload}")

        # Check if the payload has a tool_parameters field (from the API component)
        if "tool_parameters" in payload and isinstance(payload["tool_parameters"], dict):
            logger.info(f"Found 'tool_parameters' field in payload. Using it for validation.")
            # Use the tool_parameters as the actual payload for validation
            parameters = payload["tool_parameters"]
            # Keep the request_id from the original payload
            parameters["request_id"] = request_id
        else:
            # Use the original payload
            parameters = payload

        try:
            # Check for required fields
            if "input_text" not in parameters or parameters["input_text"] is None:
                error_msg = f"Missing required field 'input_text' in payload for request_id {request_id}"
                logger.error(error_msg)
                return ValidationResult(is_valid=False, error_message=error_msg, error_details={"input_text": "required field missing"})
            logger.debug(f"Required field 'input_text' is present for request_id {request_id}")

            # Validate delimiter type if present
            delimiter = parameters.get("delimiter")
            if delimiter is not None and not isinstance(delimiter, str):
                 error_msg = f"Field 'delimiter' must be a string, got {type(delimiter).__name__} for request_id {request_id}"
                 logger.error(error_msg)
                 return ValidationResult(is_valid=False, error_message=error_msg, error_details={"delimiter": "must be a string"})
            logger.debug(f"delimiter type validation passed for request_id {request_id}")

            # Validate max_splits type if present
            max_splits = parameters.get("max_splits")
            if max_splits is not None and not isinstance(max_splits, int):
                 error_msg = f"Field 'max_splits' must be an integer, got {type(max_splits).__name__} for request_id {request_id}"
                 logger.error(error_msg)
                 return ValidationResult(is_valid=False, error_message=error_msg, error_details={"max_splits": "must be an integer"})
            logger.debug(f"max_splits type validation passed for request_id {request_id}")

            # Validate include_delimiter type if present
            include_delimiter = parameters.get("include_delimiter")
            if include_delimiter is not None and not isinstance(include_delimiter, bool):
                 error_msg = f"Field 'include_delimiter' must be a boolean, got {type(include_delimiter).__name__} for request_id {request_id}"
                 logger.error(error_msg)
                 return ValidationResult(is_valid=False, error_message=error_msg, error_details={"include_delimiter": "must be a boolean"})
            logger.debug(f"include_delimiter type validation passed for request_id {request_id}")


            logger.info(f"Split text payload validation passed for request_id {request_id}")
            return ValidationResult(is_valid=True)

        except Exception as e:
            error_msg = f"Unexpected error during payload validation for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
            return ValidationResult(is_valid=False, error_message=error_msg, error_details={"validation_error": str(e)})


    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Split text into a list using a delimiter.

        Args:
            payload: The request payload

        Returns:
            The processing result
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Processing split text request for request_id: {request_id}")
        logger.debug(f"Full payload for request_id {request_id}: {payload}")

        # Check if the payload has a tool_parameters field (from the API component)
        if "tool_parameters" in payload and isinstance(payload["tool_parameters"], dict):
            logger.info(f"Found 'tool_parameters' field in payload. Using it for parameters.")
            # Use the tool_parameters as the actual payload
            parameters = payload["tool_parameters"]
            # Keep the request_id from the original payload
            parameters["request_id"] = request_id
        else:
            # Use the original payload
            parameters = payload

        try:
            # Get inputs - Use defaults from settings if not provided in payload
            input_text = parameters.get("input_text")
            delimiter = parameters.get("delimiter", settings.split_text_default_delimiter) # Load default from settings
            max_splits = parameters.get("max_splits", settings.split_text_default_max_splits) # Load default from settings
            include_delimiter = parameters.get("include_delimiter", settings.split_text_default_include_delimiter) # Load default from settings

            logger.info(f"Splitting text (length {len(str(input_text)) if input_text is not None else 0}) for request_id {request_id} with delimiter: '{delimiter}', max_splits: {max_splits}, include_delimiter: {include_delimiter}")

            # Validate input (redundant due to validate method, but good practice)
            if input_text is None:
                error_msg = f"Input text is missing for request_id {request_id}"
                logger.error(error_msg)
                # This should ideally not happen if validation passes
                return {"status": "error", "error": error_msg}

            # Convert to string if not already
            if not isinstance(input_text, str):
                logger.debug(f"Converting input_text from type {type(input_text).__name__} to string for request_id {request_id}")
                try:
                    input_text = str(input_text)
                    logger.debug(f"Converted input to string for request_id {request_id}")
                except Exception as e:
                    error_msg = f"Failed to convert input to string for request_id {request_id}: {str(e)}"
                    logger.error(error_msg)
                    logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
                    return {"status": "error", "error": error_msg}

            try:
                result = []
                if include_delimiter:
                    logger.debug(f"Splitting text including delimiter for request_id {request_id}")
                    # Use regex to split and keep the delimiter
                    if max_splits > 0:
                        logger.debug(f"Splitting with max_splits={max_splits} including delimiter for request_id {request_id}")
                        # Split with limit
                        parts = []
                        remaining = input_text
                        delimiter_len = len(delimiter)
                        split_count = 0
                        while split_count < max_splits and delimiter in remaining:
                            idx = remaining.find(delimiter)
                            parts.append(remaining[:idx + delimiter_len])
                            remaining = remaining[idx + delimiter_len:]
                            split_count += 1
                            logger.debug(f"Split {split_count} times, remaining text length: {len(remaining)} for request_id {request_id}")
                        if remaining:
                            parts.append(remaining)
                            logger.debug(f"Added remaining text for request_id {request_id}")
                        result = parts
                    else:
                        logger.debug(f"Splitting without max_splits including delimiter for request_id {request_id}")
                        # Split without limit and include delimiter
                        # Use regex to split and keep the delimiter
                        pattern = f"(.*?{re.escape(delimiter)}|.+$)"
                        result = re.findall(pattern, input_text)
                        # Remove empty strings
                        result = [part for part in result if part]
                        logger.debug(f"Regex findall resulted in {len(result)} parts for request_id {request_id}")

                else:
                    logger.debug(f"Splitting text excluding delimiter for request_id {request_id}")
                    # Use standard split method
                    result = input_text.split(delimiter, max_splits)
                    logger.debug(f"Standard split resulted in {len(result)} parts for request_id {request_id}")

                # Handle edge case: if delimiter not found, ensure we return the original text
                if not result:
                    logger.debug(f"No splits occurred, returning original text for request_id {request_id}")
                    result = [input_text]
                elif len(result) == 1 and result[0] == input_text:
                    logger.debug(f"Delimiter not found in text, returning original text for request_id {request_id}")

                # Move these logs inside the try block
                logger.info(f"Text split successfully into {len(result)} parts for request_id {request_id}")
                logger.debug(f"Split result for request_id {request_id}: {result}")
                return {"status": "success", "output_list": result}

            except Exception as e:
                error_msg = f"Error splitting text for request_id {request_id}: {str(e)}"
                logger.error(error_msg)
                logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
                return {"status": "error", "error": error_msg}

        except Exception as e:
            error_msg = f"Unexpected error processing split text request for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
            return {"status": "error", "error": error_msg}

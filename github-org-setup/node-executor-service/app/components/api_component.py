"""
API Component - Executes API requests with support for all HTTP methods (GET, POST, PUT, DELETE, PATCH, HEAD, OPTIONS, etc.).
"""

import aiohttp
from aiohttp import hdrs
import asyncio
import logging
import traceback
import json
import time
from typing import Dict, Any, Optional, List,Union

# Import ValidationResult and BaseComponent correctly
from app.core_.base_component import BaseComponent, ValidationResult
from app.core_.component_system import register_component, get_component_manager
from app.config.config import settings  # Import settings

# Import the logger configuration
try:
    from app.utils.logging_config import setup_logger

    logger = setup_logger("ApiRequestNode")
except ImportError:
    # Fallback to basic logging if logging_config is not available
    logging.basicConfig(
        level=logging.DEBUG,
        format="%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S",
    )
    logger = logging.getLogger(__name__)

def to_numeric(value: Any, target_type: Union[type[int], type[float]]) -> Optional[Union[int, float]]:
    """Safely converts a value to a numeric type (int or float)."""
    if value is None:
        return None
    if isinstance(value, (int, float)):
        return target_type(value) # Ensure it's the correct numeric type
    if isinstance(value, str) and value.strip():
        try:
            return target_type(float(value))
        except (ValueError, TypeError):
            logger.warning(f"Could not convert string '{value}' to {target_type.__name__}.")
            return None
    return None


@register_component("ApiRequestNode")
class ApiComponent(BaseComponent):
    """
    Component for executing API requests with support for all HTTP methods.

    Input Payload (`tool_parameters`):
    - url (str): Required. The target URL.
    - method (str): Optional. HTTP method (e.g., "GET", "POST"). Defaults to "GET".
    - headers (dict): Optional. Request headers.
    - query_params (dict): Optional. URL query parameters.
    - body (any): Optional. Raw request body (e.g., string, bytes). Used if 'json' is not provided.
    - json (any): Optional. JSON request body. Takes precedence over 'body' if both are present.
    - timeout (float): Optional. Request timeout in seconds. Defaults to settings.api_default_timeout.
    - max_content_length (int): Optional. Max response body size in bytes. Defaults to settings.api_default_max_content_length (0 = no limit).
    - target_component (str): Optional. Metadata field.

    Output Result:
    - status_code (int): HTTP status code received (or 0/error code on failure).
    - headers (dict): Response headers.
    - content_type (str): Response content type.
    - data (any): Response body content (parsed as JSON if possible, otherwise text).
    - method (str): The HTTP method used for the request.
    - url (str): The final URL after any redirects.
    - error (str | None): Error message if the request failed before getting a response or during processing.
    """

    def __init__(self):
        """
        Initialize the API component.
        """
        logger.info("Initializing API Component")
        try:
            super().__init__()
            logger.info("API Component initialized successfully")
        except Exception as e:
            logger.error(f"Error initializing API Component: {str(e)}")
            logger.debug(f"Exception details: {traceback.format_exc()}")
            raise

    async def validate(self, payload: Dict[str, Any]) -> ValidationResult:
        """
        Validate an API request payload.

        Args:
            payload: The payload to validate (expected to be tool_parameters)

        Returns:
            ValidationResult with validation status
        """
        request_id = payload.get(
            "request_id", "unknown"
        )  # Assuming request_id might be present here too
        logger.info(f"Validating API request payload for request_id: {request_id}")
        logger.debug(
            f"Payload received for validation (request_id={request_id}): {payload}"
        )

        try:
            # Check for required fields
            if "url" not in payload or not payload["url"]:
                error_msg = f"Missing or empty required field 'url' in payload for request_id {request_id}"
                logger.error(error_msg)
                return ValidationResult(
                    is_valid=False,
                    error_message=error_msg,
                    error_details={"url": "required field missing or empty"},
                )

            # Method validation
            method = payload.get("method").upper()
            logger.info(f"Validating HTTP method: {method} for request_id {request_id}")
            standard_methods = [
                "GET",
                "POST",
                "PUT",
                "DELETE",
                "PATCH",
                "HEAD",
                "OPTIONS",
            ]
            if method not in standard_methods:
                logger.warning(
                    f"Potentially non-standard HTTP method requested: {method} for request_id {request_id}. Proceeding."
                )

            # Check for body/json presence for relevant methods
            if method in ["POST", "PUT", "PATCH"]:
                # Use "body" for raw data, "json" for structured JSON
                if not payload.get("body") and not payload.get("json"):
                    logger.warning(
                        f"{method} request without 'body' or 'json' data for request_id {request_id}. This may be intentional."
                    )

            # Timeout validation
            timeout = 900.0
            # if timeout is not None:
            #     if not isinstance(timeout, (int, float)) or timeout <= 0:
            #         error_msg = f"Invalid timeout value: {timeout}. Must be a positive number for request_id {request_id}"
            #         logger.error(error_msg)
            #         return ValidationResult(
            #             is_valid=False,
            #             error_message=error_msg,
            #             error_details={"timeout": "must be a positive number"},
            #         )

            # Max content length validation
            max_content_length = payload.get("max_content_length")
            if max_content_length is not None:
                if not isinstance(max_content_length, int) or max_content_length < 0:
                    error_msg = f"Invalid max_content_length value: {max_content_length}. Must be a non-negative integer for request_id {request_id}"
                    logger.error(error_msg)
                    return ValidationResult(
                        is_valid=False,
                        error_message=error_msg,
                        error_details={
                            "max_content_length": "must be a non-negative integer"
                        },
                    )

            # Validate headers if present
            if "headers" in payload and payload["headers"] is not None:
                headers = payload["headers"]
                if not isinstance(headers, dict):
                    error_msg = f"Headers must be a dictionary, got {type(headers).__name__} for request_id {request_id}"
                    logger.error(error_msg)
                    return ValidationResult(
                        is_valid=False,
                        error_message=error_msg,
                        error_details={"headers": "must be a dictionary"},
                    )

            # Validate query_params if present
            if "query_params" in payload and payload["query_params"] is not None:
                params = payload["query_params"]
                if not isinstance(params, dict):
                    error_msg = f"Query params must be a dictionary, got {type(params).__name__} for request_id {request_id}"
                    logger.error(error_msg)
                    return ValidationResult(
                        is_valid=False,
                        error_message=error_msg,
                        error_details={"query_params": "must be a dictionary"},
                    )

            logger.info(
                f"API request payload validation passed for request_id {request_id}"
            )
            return ValidationResult(is_valid=True)

        except Exception as e:
            error_msg = f"Unexpected error during payload validation for request_id {request_id}: {str(e)}"
            logger.error(
                error_msg, exc_info=True
            )  # Log traceback for unexpected validation errors
            return ValidationResult(
                is_valid=False,
                error_message=error_msg,
                error_details={"validation_error": str(e)},
            )

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute an API request based on the tool_parameters.

        Args:
            payload: The request payload (expected to be tool_parameters)

        Returns:
            A dictionary representing the API response or an error.
        """
        request_id = payload.get(
            "request_id", "unknown"
        )  # Check if request_id is passed within tool_parameters
        logger.info(f"Starting API request processing for request_id: {request_id}")
        logger.debug(
            f"Full payload (tool_parameters) for request_id {request_id}: {payload}"
        )

        # --- Initialize variables ---
        url: Optional[str] = None
        method: str = "GET"
        request_body_raw = None  # To hold data from payload['body']
        request_body_json = None  # To hold data from payload['json']
        result: Dict[str, Any] = {}  # Initialize result dict

        try:
            # --- Extract request parameters from payload ---
            url = payload["url"]  # Required, checked in validate
            method = payload.get("method").upper()
            headers = payload.get("headers") or {}
            params = payload.get("query_params") or {}
            logger.info(f"Extracting request new parameters {url}, {method}, {headers}, {params} for request_id {request_id}")
            # Get potential request bodies - INPUT KEYS ARE 'body' and 'json'
            request_body_raw = payload.get("body")
            request_body_json = payload.get("json")

            # Handle dual-purpose input value wrapping
            # If body is a dict with a single "value" key, extract the actual value
            if isinstance(request_body_raw, dict) and len(request_body_raw) == 1 and "value" in request_body_raw:
                logger.info(f"Detected dual-purpose input wrapper for 'body'. Extracting value from: {request_body_raw}")
                request_body_raw = request_body_raw["value"]
                logger.info(f"Extracted body value: {request_body_raw} (type: {type(request_body_raw)})")

                # If the extracted value is a JSON string, try to parse it
                if isinstance(request_body_raw, str):
                    try:
                        parsed_body = json.loads(request_body_raw)
                        logger.info(f"Successfully parsed JSON string body: {parsed_body}")
                        request_body_raw = parsed_body
                    except json.JSONDecodeError as e:
                        logger.debug(f"Body value is a string but not valid JSON, keeping as string: {e}")

            # Similarly handle json parameter if it has value wrapper
            if isinstance(request_body_json, dict) and len(request_body_json) == 1 and "value" in request_body_json:
                logger.info(f"Detected dual-purpose input wrapper for 'json'. Extracting value from: {request_body_json}")
                request_body_json = request_body_json["value"]
                logger.info(f"Extracted json value: {request_body_json} (type: {type(request_body_json)})")

                # If the extracted value is a JSON string, try to parse it
                if isinstance(request_body_json, str):
                    try:
                        parsed_json = json.loads(request_body_json)
                        logger.info(f"Successfully parsed JSON string: {parsed_json}")
                        request_body_json = parsed_json
                    except json.JSONDecodeError as e:
                        logger.debug(f"JSON value is a string but not valid JSON, keeping as string: {e}")

            logger.info(f"Final request body values - raw: {request_body_raw}, json: {request_body_json} for request_id {request_id}")
            timeout = 900
            
            max_content_length = payload.get(
                "max_content_length", settings.api_default_max_content_length
            )
            target_component = payload.get("target_component")  # Metadata

            logger.info(
                f"Request parameters extracted for request_id {request_id}: URL={url}, Method={method}, Timeout={timeout} , MaxContentLength={max_content_length}, TargetComponent={target_component}"
            )
            logger.debug(
                f"Headers: {headers}, Params: {params}, Body Input Provided: {request_body_raw is not None}, JSON Input Provided: {request_body_json is not None}"
            )

            # --- Prepare request for aiohttp ---
            # Prioritize 'json' over 'body' if both are provided
            aiohttp_data_kwarg = None
            aiohttp_json_kwarg = None

            # Get Content-Type header (case-insensitive)
            header_keys_lower = {k.lower(): k for k in headers}
            content_type_header = ""
            if "content-type" in header_keys_lower:
                content_type_key = header_keys_lower["content-type"]
                content_type_header = headers.get(content_type_key, "").lower()

            # Check if Content-Type indicates JSON
            is_json_content_type = "application/json" in content_type_header

            # Handle request body based on input and Content-Type
            if request_body_json is not None:
                # Always use json parameter if explicitly provided in the 'json' field
                aiohttp_json_kwarg = request_body_json
                if request_body_raw is not None:
                    logger.warning(
                        f"Both 'body' and 'json' provided in input for request_id {request_id}. Using 'json' and ignoring 'body'."
                    )
            elif request_body_raw is not None:
                # Handle 'body' field based on its type and Content-Type header
                if isinstance(request_body_raw, dict):
                    # If Content-Type is application/json, serialize dict to JSON string and send as data
                    # This ensures proper JSON formatting while respecting the Content-Type header
                    if is_json_content_type:
                        aiohttp_data_kwarg = json.dumps(request_body_raw)
                        logger.info(
                            f"Serializing dict from 'body' to JSON string for data parameter (request_id={request_id})"
                        )
                    else:
                        # If no JSON Content-Type, use json parameter to prevent automatic form encoding
                        aiohttp_json_kwarg = request_body_raw
                        logger.debug(
                            f"Using dict from 'body' as JSON data (request_id={request_id})"
                        )
                elif isinstance(request_body_raw, str) and is_json_content_type:
                    # If body is a string and Content-Type is application/json, send as raw string
                    aiohttp_data_kwarg = request_body_raw
                    logger.info(
                        f"Using JSON string from 'body' as raw data (request_id={request_id})"
                    )
                else:
                    # For all other cases, use the data parameter
                    aiohttp_data_kwarg = request_body_raw

            # Do not set any default Content-Type header
            # If Content-Type is not provided, it will remain null
            # This ensures we only use Content-Type values explicitly provided by the user
            if "content-type" not in header_keys_lower:
                logger.debug(
                    f"No Content-Type header provided in payload (request_id={request_id}). Sending request with null Content-Type header."
                )

            # --- Execute request ---
            logger.debug(f"Creating aiohttp ClientSession (request_id: {request_id})")

            # Create a custom request class that doesn't automatically set Content-Type for JSON
            class NoAutoContentTypeClientRequest(aiohttp.ClientRequest):
                @property
                def skip_auto_headers(self):
                    # Skip automatically setting Content-Type header if not explicitly provided
                    if "content-type" not in header_keys_lower and aiohttp_json_kwarg is not None:
                        return {hdrs.CONTENT_TYPE, *super().skip_auto_headers}
                    return super().skip_auto_headers

            # Create connector with SSL verification disabled
            connector = aiohttp.TCPConnector(ssl=False)

            async with aiohttp.ClientSession(
                request_class=NoAutoContentTypeClientRequest,
                connector=connector
            ) as session:
                # Log detailed request information
                logger.info(
                    f"[HTTP REQUEST START] Method: {method}, URL: {url}, Timeout: {timeout}s, RequestID: {request_id}"
                )

                # Log request headers and parameters
                if headers:
                    logger.info(
                        f"[HTTP REQUEST HEADERS] {json.dumps(headers, indent=2)}, RequestID: {request_id}"
                    )
                if params:
                    logger.info(
                        f"[HTTP REQUEST PARAMS] {json.dumps(params, indent=2)}, RequestID: {request_id}"
                    )

                # Log request body based on type
                if aiohttp_json_kwarg:
                    logger.info(
                        f"[HTTP REQUEST BODY] JSON: {json.dumps(aiohttp_json_kwarg, indent=2)}, RequestID: {request_id}"
                    )
                elif aiohttp_data_kwarg:
                    if isinstance(aiohttp_data_kwarg, dict):
                        # Determine if this is form data or JSON based on Content-Type
                        if is_json_content_type:
                            logger.info(
                                f"[HTTP REQUEST BODY] JSON data: {json.dumps(aiohttp_data_kwarg, indent=2)}, RequestID: {request_id}"
                            )
                        else:
                            logger.info(
                                f"[HTTP REQUEST BODY] Form data: {json.dumps(aiohttp_data_kwarg, indent=2)}, RequestID: {request_id}"
                            )
                    elif isinstance(aiohttp_data_kwarg, (str, bytes)):
                        body_preview = (
                            str(aiohttp_data_kwarg)[:200] + "..."
                            if len(str(aiohttp_data_kwarg)) > 200
                            else str(aiohttp_data_kwarg)
                        )
                        # Check if this might be a JSON string
                        if is_json_content_type:
                            logger.info(
                                f"[HTTP REQUEST BODY] JSON string (preview): {body_preview}, RequestID: {request_id}"
                            )
                        else:
                            logger.info(
                                f"[HTTP REQUEST BODY] Raw data (preview): {body_preview}, RequestID: {request_id}"
                            )

                logger.info(f"[HTTP Method]: {method},[HTTP URL] : {url} RequestID: {request_id}")
                logger.info(f"[HTTP BODY] {aiohttp_data_kwarg}")
                logger.info(f"[HTTP JSON BODY] {aiohttp_json_kwarg}")
                # Execute the request with timing
                request_start_time = time.time()
                async with session.request(
                    method=method,
                    url=url,
                    headers=headers,
                    params=params,
                    # Pass extracted values to aiohttp's specific keyword arguments
                    data=aiohttp_data_kwarg,
                    json=aiohttp_json_kwarg,
                    timeout=aiohttp.ClientTimeout(total=timeout),
                ) as response:
                    request_duration = time.time() - request_start_time

                    # Log request completion time
                    logger.info(
                        f"[HTTP REQUEST COMPLETED] Duration: {request_duration:.3f}s, Status: {response.status}, RequestID: {request_id}"
                    )

                    # Log detailed response information
                    logger.info(
                        f"[HTTP RESPONSE] Status: {response.status}, URL: {response.url}, Method: {method}, RequestID: {request_id} "
                    )

                    # Log response headers
                    logger.info(
                        f"[HTTP RESPONSE HEADERS] {json.dumps(dict(response.headers), indent=2)}, RequestID: {request_id}"
                    )

                    # Log response content type and length
                    content_type = response.content_type or response.headers.get(
                        "Content-Type", "unknown"
                    )
                    content_length_str = response.headers.get(
                        "Content-Length", "unknown"
                    )
                    logger.info(
                        f"[HTTP RESPONSE CONTENT] Type: {content_type}, Length: {content_length_str}, RequestID: {request_id}"
                    )

                    # --- Prepare result structure ---
                    result = {
                        "status_code": response.status,
                        "headers": dict(response.headers),
                        "content_type": response.content_type,
                        "data": None,  # Initialize response data field - OUTPUT KEY IS 'data'
                        "method": method,
                        "url": str(response.url),
                        "error": None,
                    }

                    # --- Process response body ---
                    response_data_content = (
                        None  # Variable to hold the processed response content
                    )
                    content_length_str = response.headers.get("Content-Length")
                    content_length = 0
                    if content_length_str and content_length_str.isdigit():
                        content_length = int(content_length_str)
                    logger.debug(
                        f"Response Content-Length: {content_length_str} (parsed as {content_length}) (request_id: {request_id})"
                    )

                    if method == "HEAD":
                        logger.debug(
                            f"HEAD request - skipping body processing (request_id: {request_id})"
                        )
                        response_data_content = ""
                    elif max_content_length > 0 and content_length > max_content_length:
                        error_msg = f"Response content length ({content_length} bytes) exceeds limit ({max_content_length} bytes)."
                        logger.warning(f"{error_msg} Request_id: {request_id}")
                        result["error"] = error_msg
                        response_data_content = (
                            f"<Response content truncated: {error_msg}>"
                        )
                        try:
                            await response.read()  # Discard body
                        except Exception as read_err:
                            logger.warning(
                                f"Error discarding oversized response body for request_id {request_id}: {read_err}"
                            )
                    else:
                        try:
                            if response.content_type == "application/json":
                                logger.debug(
                                    f"Attempting to parse JSON response (request_id: {request_id})"
                                )
                                response_data_content = await response.json()
                            else:
                                logger.debug(
                                    f"Processing non-JSON response as text (request_id: {request_id})"
                                )
                                response_data_content = await response.text()
                        except json.JSONDecodeError as json_err:
                            logger.warning(
                                f"Failed to parse JSON response for request_id {request_id}: {str(json_err)}. Falling back to text."
                            )
                            try:
                                response_data_content = await response.text()
                            except Exception as text_err:
                                error_msg = f"Failed to read response body as text after JSON error for request_id {request_id}: {text_err}"
                                logger.error(error_msg)
                                result["error"] = (
                                    (result["error"] + "; " + error_msg)
                                    if result["error"]
                                    else error_msg
                                )
                                response_data_content = (
                                    f"<Error reading response body: {text_err}>"
                                )
                        except aiohttp.ClientPayloadError as payload_err:
                            error_msg = f"Payload error reading response body for request_id {request_id}: {payload_err}"
                            logger.error(error_msg)
                            result["error"] = (
                                (result["error"] + "; " + error_msg)
                                if result["error"]
                                else error_msg
                            )
                            response_data_content = (
                                f"<Error reading response body: {payload_err}>"
                            )
                        except Exception as read_err:
                            error_msg = f"Unexpected error reading response body for request_id {request_id}: {read_err}"
                            logger.error(error_msg, exc_info=True)
                            result["error"] = (
                                (result["error"] + "; " + error_msg)
                                if result["error"]
                                else error_msg
                            )
                            response_data_content = (
                                f"<Error reading response body: {read_err}>"
                            )

                    # Process the response data based on the request and response
                    # For httpbin.org and similar testing services that return a structured response
                    if response.content_type == "application/json" and isinstance(
                        response_data_content, dict
                    ):
                        # Check if this is a JSON request and the response has a 'json' field
                        if (
                            is_json_content_type or aiohttp_json_kwarg is not None
                        ) and "json" in response_data_content:
                            # For httpbin.org and similar services, extract the 'json' field which contains the original data
                            logger.debug(
                                f"Detected httpbin-like response structure with 'json' field (request_id={request_id}). Extracting original data."
                            )
                            # Use the 'json' field as the main data response to match the original request format
                            result["data"] = response_data_content["json"]

                        # Check if this is a form data request and the response has a 'form' field
                        elif (
                            not is_json_content_type
                            and aiohttp_data_kwarg is not None
                            and isinstance(aiohttp_data_kwarg, dict)
                            and "form" in response_data_content
                        ):
                            # For form data requests, extract the 'form' field which contains the original data
                            logger.debug(
                                f"Detected httpbin-like response structure with 'form' field (request_id={request_id}). Extracting form data."
                            )
                            # Use the 'form' field as the main data response to match the original request format
                            result["data"] = response_data_content["form"]
                        else:
                            # For regular JSON responses, use the parsed JSON
                            result["data"] = response_data_content
                    else:
                        # For all other cases, use the processed response content as is
                        result["data"] = response_data_content

                    # Log response body (with truncation for large responses)
                    if response_data_content is not None:
                        if isinstance(response_data_content, dict) or isinstance(
                            response_data_content, list
                        ):
                            try:
                                body_json = json.dumps(response_data_content, indent=2)
                                if len(body_json) > 1000:
                                    logger.info(
                                        f"[HTTP RESPONSE BODY] JSON (truncated): {body_json[:1000]}..., RequestID: {request_id}"
                                    )
                                else:
                                    logger.info(
                                        f"[HTTP RESPONSE BODY] JSON: {body_json}, RequestID: {request_id}"
                                    )
                            except Exception as json_err:
                                logger.warning(
                                    f"Error serializing response body for logging: {str(json_err)}, RequestID: {request_id}"
                                )
                        elif isinstance(response_data_content, str):
                            if len(response_data_content) > 1000:
                                logger.info(
                                    f"[HTTP RESPONSE BODY] Text (truncated): {response_data_content[:1000]}..., RequestID: {request_id}"
                                )
                            else:
                                logger.info(
                                    f"[HTTP RESPONSE BODY] Text: {response_data_content}, RequestID: {request_id}"
                                )
                        else:
                            logger.info(
                                f"[HTTP RESPONSE BODY] Type: {type(response_data_content).__name__}, RequestID: {request_id}"
                            )

                    # Log final status and handle errors
                    if not result["error"] and 200 <= response.status < 300:
                        logger.info(
                            f"API request successful: Status={response.status}, RequestID={request_id}"
                        )
                    elif 400 <= response.status < 500:
                        # Set error message for 4xx status codes with more details
                        status_text = {
                            400: "Bad Request",
                            401: "Unauthorized",
                            403: "Forbidden",
                            404: "Not Found",
                            405: "Method Not Allowed",
                            406: "Not Acceptable",
                            407: "Proxy Authentication Required",
                            408: "Request Timeout",
                            409: "Conflict",
                            410: "Gone",
                            411: "Length Required",
                            412: "Precondition Failed",
                            413: "Payload Too Large",
                            414: "URI Too Long",
                            415: "Unsupported Media Type",
                            416: "Range Not Satisfiable",
                            417: "Expectation Failed",
                            418: "I'm a teapot",
                            421: "Misdirected Request",
                            422: "Unprocessable Entity",
                            423: "Locked",
                            424: "Failed Dependency",
                            425: "Too Early",
                            426: "Upgrade Required",
                            428: "Precondition Required",
                            429: "Too Many Requests",
                            431: "Request Header Fields Too Large",
                            451: "Unavailable For Legal Reasons"
                        }.get(response.status, "Client Error")

                        # Only set error if not already set
                        if not result["error"]:
                            error_msg = f"API request failed with status {response.status} ({status_text})"

                            # Check for Fault details in response
                            if response_data_content and isinstance(response_data_content, dict):
                                if "Fault" in response_data_content:
                                    # Include Fault details from response if available
                                    try:
                                        fault_details = json.dumps(response_data_content["Fault"], indent=2)
                                        error_msg += f", Details: {fault_details}"
                                    except:
                                        pass
                                # Also check for error or message fields
                                elif "error" in response_data_content or "message" in response_data_content:
                                    error_details = response_data_content.get("error", response_data_content.get("message", ""))
                                    error_msg += f": {error_details}"
                            # Include simple string response if it's short
                            elif isinstance(response_data_content, str) and len(response_data_content) < 200:
                                error_msg += f": {response_data_content}"

                            result["error"] = error_msg

                        logger.warning(
                            f"API request failed (Client Error): Status={response.status} ({status_text}), RequestID={request_id}, Error: {result['error']}"
                        )

                        # For 4xx errors, return a result with error information
                        # The component_system will handle sending it as an error response
                        return {
                            "status": "error",
                            "error": result["error"],
                            "status_code": response.status,
                            # Include the original result data for reference
                            "data": result.get("data")
                        }
                    elif result["error"]:
                        logger.warning(
                            f"API request completed with processing error: Status={response.status}, RequestID={request_id}, Error: {result['error']}"
                        )
                    elif 500 <= response.status < 600:
                        # Set error message for 5xx status codes
                        status_text = {
                            500: "Internal Server Error",
                            501: "Not Implemented",
                            502: "Bad Gateway",
                            503: "Service Unavailable",
                            504: "Gateway Timeout",
                            505: "HTTP Version Not Supported",
                            506: "Variant Also Negotiates",
                            507: "Insufficient Storage",
                            508: "Loop Detected",
                            510: "Not Extended",
                            511: "Network Authentication Required"
                        }.get(response.status, "Server Error")

                        # Only set error if not already set
                        if not result["error"]:
                            error_msg = f"API request failed with status {response.status} ({status_text})"

                            # Check for Fault details in response
                            if response_data_content and isinstance(response_data_content, dict) and "Fault" in response_data_content:
                                # Include Fault details from response if available
                                try:
                                    fault_details = json.dumps(response_data_content["Fault"], indent=2)
                                    error_msg += f", Details: {fault_details}"
                                except:
                                    pass

                            result["error"] = error_msg

                        logger.error(
                            f"API request failed (Server Error): Status={response.status} ({status_text}), RequestID={request_id}, Error: {result['error']}"
                        )

                        # For 5xx errors, return a result with error information
                        # The component_system will handle sending it as an error response
                        return {
                            "status": "error",
                            "error": result["error"],
                            "status_code": response.status,
                            # Include the original result data for reference
                            "data": result.get("data")
                        }
                    else:
                        # This covers 1xx, 3xx, or other unexpected status codes
                        logger.info(
                            f"API request completed with status: Status={response.status}, RequestID={request_id}"
                        )

                    logger.debug(
                        f"Returning result for request_id {request_id}: {result}"
                    )
                    return result  # Return the populated result dictionary

        # --- Exception Handling for Connection/Request Errors ---
        except aiohttp.ClientConnectorError as e:
            error_msg = f"Connection error (request_id {request_id}): {str(e)}"
            logger.error(f"[HTTP ERROR] {error_msg}")
            logger.debug(f"Details: {traceback.format_exc()}")
            result = {
                "status_code": 0,
                "error": error_msg,
                "headers": {},
                "content_type": "",
                "data": None,
                "method": method,
                "url": url,
            }
            logger.info(
                f"[HTTP REQUEST FAILED] Method: {method}, URL: {url}, Error: Connection error, RequestID: {request_id}"
            )
            return result
        except aiohttp.ClientError as e:
            error_msg = f"HTTP client error (request_id {request_id}): {str(e)}"
            logger.error(f"[HTTP ERROR] {error_msg}")
            logger.debug(f"Details: {traceback.format_exc()}")
            result = {
                "status_code": 0,
                "error": error_msg,
                "headers": {},
                "content_type": "",
                "data": None,
                "method": method,
                "url": url,
            }
            logger.info(
                f"[HTTP REQUEST FAILED] Method: {method}, URL: {url}, Error: Client error, RequestID: {request_id}"
            )
            return result
        except asyncio.TimeoutError:
            error_msg = f"Request timed out after {payload.get('timeout', 900)}s (request_id {request_id})"
            logger.error(f"[HTTP ERROR] {error_msg}")
            result = {
                "status_code": 408,
                "error": error_msg,
                "headers": {},
                "content_type": "",
                "data": None,
                "method": method,
                "url": url,
            }
            logger.info(
                f"[HTTP REQUEST FAILED] Method: {method}, URL: {url}, Error: Timeout, RequestID: {request_id}"
            )
            return result
        except KeyError as e:
            # Should be caught by validation, but as a safeguard
            error_msg = f"Missing required parameter '{str(e)}' in payload (request_id {request_id})"
            logger.error(f"[HTTP ERROR] {error_msg}")
            logger.debug(f"Details: {traceback.format_exc()}")
            result = {
                "status_code": 400,
                "error": error_msg,
                "headers": {},
                "content_type": "",
                "data": None,
                "method": method,
                "url": url,
            }
            logger.info(
                f"[HTTP REQUEST FAILED] Method: {method}, URL: {url}, Error: Missing parameter, RequestID: {request_id}"
            )
            return result
        except Exception as e:
            # Catch-all for unexpected errors during setup or request initiation
            error_msg = f"Unexpected error processing API request (request_id {request_id}): {str(e)}"
            logger.error(
                f"[HTTP ERROR] {error_msg}", exc_info=True
            )  # Log full traceback
            result = {
                "status_code": 500,
                "error": error_msg,
                "headers": {},
                "content_type": "",
                "data": None,
                "method": method,
                "url": url,
            }
            logger.info(
                f"[HTTP REQUEST FAILED] Method: {method}, URL: {url}, Error: Unexpected error, RequestID: {request_id}"
            )
            return result

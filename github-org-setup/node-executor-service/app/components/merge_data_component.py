"""
Merge Data Component - Combines multiple dictionaries or lists.

This component takes a main input and a configurable number of additional inputs
(dictionaries or lists) and combines them according to the specified merge strategy.
"""

import logging
import traceback
import copy
from typing import Dict, Any, List, Optional, Union
from pydantic import BaseModel, Field, ValidationError, field_validator

from app.core_.base_component import BaseComponent, ValidationResult
from app.core_.component_system import register_component

# Set up logging
try:
    from app.utils.logging_config import setup_logger
    logger = setup_logger("MergeDataComponent")
except ImportError:
    # Fallback to basic logging if logging_config is not available
    logging.basicConfig(
        level=logging.INFO,
        format="%(asctime)s - %(name)s - %(levelname)s - [%(funcName)s:%(lineno)d] %(message)s",
        datefmt="%Y-%m-%d %H:%M:%S"
    )
    logger = logging.getLogger(__name__)


# Define request schema using Pydantic
class MergeDataRequest(BaseModel):
    """
    Schema for merge data request.
    """
    main_input: Any = Field(..., description="Main data structure to merge (list or dict)")
    num_additional_inputs: int = Field(2, description="Number of additional inputs to process")
    merge_strategy: str = Field("Overwrite", description="Strategy for merging dictionaries: Overwrite, Deep Merge, Error on Conflict, Aggregate, or Structured Compose")
    output_key_1: Optional[str] = Field("data_1", description="Custom key name for main input (Structured Compose only)")
    output_key_2: Optional[str] = Field("data_2", description="Custom key name for first additional input (Structured Compose only)")
    output_key_3: Optional[str] = Field("data_3", description="Custom key name for second additional input (Structured Compose only)")
    output_key_4: Optional[str] = Field("data_4", description="Custom key name for third additional input (Structured Compose only)")
    output_key_5: Optional[str] = Field("data_5", description="Custom key name for fourth additional input (Structured Compose only)")
    output_key_6: Optional[str] = Field("data_6", description="Custom key name for fifth additional input (Structured Compose only)")
    output_key_7: Optional[str] = Field("data_7", description="Custom key name for sixth additional input (Structured Compose only)")
    output_key_8: Optional[str] = Field("data_8", description="Custom key name for seventh additional input (Structured Compose only)")
    output_key_9: Optional[str] = Field("data_9", description="Custom key name for eighth additional input (Structured Compose only)")
    output_key_10: Optional[str] = Field("data_10", description="Custom key name for ninth additional input (Structured Compose only)")
    output_key_11: Optional[str] = Field("data_11", description="Custom key name for tenth additional input (Structured Compose only)")
    # Dynamic inputs - we'll handle these in the validation and processing methods
    input_1: Optional[Any] = Field(None, description="Additional input 1")
    input_2: Optional[Any] = Field(None, description="Additional input 2")
    input_3: Optional[Any] = Field(None, description="Additional input 3")
    input_4: Optional[Any] = Field(None, description="Additional input 4")
    input_5: Optional[Any] = Field(None, description="Additional input 5")
    input_6: Optional[Any] = Field(None, description="Additional input 6")
    input_7: Optional[Any] = Field(None, description="Additional input 7")
    input_8: Optional[Any] = Field(None, description="Additional input 8")
    input_9: Optional[Any] = Field(None, description="Additional input 9")
    input_10: Optional[Any] = Field(None, description="Additional input 10")

    # Add validator to ensure main_input is provided
    @field_validator('main_input')
    def check_main_input(cls, v):
        if v is None:
            raise ValueError("Main input is required")
        return v

    # Add validator to ensure merge_strategy is valid
    @field_validator('merge_strategy')
    def check_merge_strategy(cls, v):
        valid_strategies = ["Overwrite", "Deep Merge", "Error on Conflict", "Aggregate", "Structured Compose"]
        if v not in valid_strategies:
            raise ValueError(f"Invalid merge strategy: {v}. Must be one of: {', '.join(valid_strategies)}")
        return v

    # Add validator to ensure num_additional_inputs is valid
    @field_validator('num_additional_inputs')
    def check_num_additional_inputs(cls, v):
        if not isinstance(v, int) or v < 0 or v > 10:
            raise ValueError(f"Number of additional inputs must be between 0 and 10, got {v}")
        return v


@register_component("MergeDataComponent")
class MergeDataExecutor(BaseComponent):
    """
    Component for merging multiple data structures (lists or dictionaries).

    This component can:
    1. Merge multiple lists by concatenation
    2. Merge multiple dictionaries using different strategies:
       - Overwrite: Simple update (later dicts overwrite keys in earlier ones)
       - Deep Merge: Recursively merge nested dictionaries
       - Error on Conflict: Fail if there are key conflicts
    """

    # Maximum number of additional inputs to support
    MAX_ADDITIONAL_INPUTS = 10

    def __init__(self):
        """
        Initialize the MergeDataExecutor component.
        """
        super().__init__()
        self.request_schema = MergeDataRequest
        logger.info("MergeDataExecutor initialized")

    def _deep_merge(self, dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
        """
        Recursively merges two dictionaries.

        Args:
            dict1: The first dictionary
            dict2: The second dictionary

        Returns:
            The merged dictionary
        """
        logger.debug("Starting deep merge")
        result = copy.deepcopy(dict1)

        for key, value in dict2.items():
            logger.debug(f"Processing key '{key}' for deep merge")
            # If both values are dictionaries, recursively update
            if key in result and isinstance(result[key], dict) and isinstance(value, dict):
                logger.debug(f"Recursively updating nested dictionary for key '{key}'")
                result[key] = self._deep_merge(result[key], value)
            else:
                # Otherwise, simply overwrite or add the value
                logger.debug(f"Updating or adding key '{key}' with value type {type(value).__name__}")
                result[key] = copy.deepcopy(value)

        logger.debug("Deep merge completed")
        return result

    def _aggregate_merge(self, dict1: Dict[str, Any], dict2: Dict[str, Any]) -> Dict[str, Any]:
        """
        Merges two dictionaries by aggregating values for common keys.
        If a key exists in both, the values are collected into a list.
        """
        logger.debug("Starting aggregate merge")
        result = copy.deepcopy(dict1)

        for key, value in dict2.items():
            logger.debug(f"Processing key '{key}' for aggregate merge")
            if key in result:
                # If key exists, aggregate values
                if isinstance(result[key], list):
                    # If existing value is a list, append or extend
                    if isinstance(value, list):
                        result[key].extend(copy.deepcopy(value))
                    else:
                        result[key].append(copy.deepcopy(value))
                else:
                    # If existing value is not a list, create a new list
                    result[key] = [result[key], copy.deepcopy(value)]
            else:
                # If key does not exist, just add it
                result[key] = copy.deepcopy(value)
        
        logger.debug("Aggregate merge completed")
        return result

    async def validate(self, payload: Dict[str, Any]) -> ValidationResult:
        """
        Validate the merge data request payload.

        Args:
            payload: The payload to validate

        Returns:
            ValidationResult with validation status
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Validating merge data request for request_id: {request_id}")
        logger.debug(f"Payload for validation (request_id={request_id}): {payload}")

        # Check if the payload has a tool_parameters field (from the API component)
        if "tool_parameters" in payload and isinstance(payload["tool_parameters"], dict):
            logger.info(f"Found 'tool_parameters' field in payload. Using it for validation.")
            # Use the tool_parameters as the actual payload for validation
            parameters = payload["tool_parameters"]
            # Keep the request_id from the original payload
            parameters["request_id"] = request_id
        else:
            # Use the original payload
            parameters = payload

        logger.info(f"VALIDATION PARAMETERS KEYS: {list(parameters.keys())}")

        try:
            # First use schema validation (from parent class)
            # We still validate the original payload for backward compatibility
            schema_validation = await super().validate(payload)
            if not schema_validation.is_valid:
                # Try validating the parameters instead
                try:
                    MergeDataRequest(**parameters)
                except ValidationError as e:
                    logger.error(f"Schema validation failed for request_id {request_id}: {schema_validation.error_message}")
                    return schema_validation
            logger.debug(f"Schema validation passed for request_id {request_id}")

            # Check for main_input
            if "main_input" not in parameters:
                error_msg = f"Required field 'main_input' not found in parameters for request_id {request_id}"
                logger.error(error_msg)
                logger.error(f"Available keys in parameters: {list(parameters.keys())}")
                return ValidationResult(
                    is_valid=False,
                    error_message=error_msg,
                    error_details={"main_input": "The main_input field is required"}
                )

            # Validate num_additional_inputs
            num_additional_inputs = parameters.get("num_additional_inputs")
            if num_additional_inputs is not None:
                try:
                    num_additional_inputs = int(num_additional_inputs)
                    if num_additional_inputs < 0 or num_additional_inputs > self.MAX_ADDITIONAL_INPUTS:
                        error_msg = f"Field 'num_additional_inputs' must be between 0 and {self.MAX_ADDITIONAL_INPUTS}, got {num_additional_inputs} for request_id {request_id}"
                        logger.error(error_msg)
                        return ValidationResult(
                            is_valid=False,
                            error_message=error_msg,
                            error_details={"num_additional_inputs": f"must be between 0 and {self.MAX_ADDITIONAL_INPUTS}"}
                        )
                except (ValueError, TypeError):
                    error_msg = f"Field 'num_additional_inputs' must be an integer, got {type(num_additional_inputs).__name__} for request_id {request_id}"
                    logger.error(error_msg)
                    return ValidationResult(
                        is_valid=False,
                        error_message=error_msg,
                        error_details={"num_additional_inputs": "must be an integer"}
                    )

            # Validate merge_strategy
            merge_strategy = parameters.get("merge_strategy", "Overwrite")
            valid_strategies = ["Overwrite", "Deep Merge", "Error on Conflict", "Aggregate", "Structured Compose"]
            if merge_strategy not in valid_strategies:
                error_msg = f"Invalid merge strategy for request_id {request_id}: {merge_strategy}. Must be one of: {', '.join(valid_strategies)}"
                logger.error(error_msg)
                return ValidationResult(
                    is_valid=False,
                    error_message=error_msg,
                    error_details={"invalid_strategy": merge_strategy}
                )

            logger.info(f"Merge data payload validation passed for request_id {request_id}")
            return ValidationResult(is_valid=True)

        except Exception as e:
            error_msg = f"Unexpected error during payload validation for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
            return ValidationResult(
                is_valid=False,
                error_message=error_msg,
                error_details={"validation_error": str(e)}
            )

    async def process(self, payload: Dict[str, Any]) -> Dict[str, Any]:
        """
        Execute the merge operation on multiple input data structures.

        Args:
            payload: The request payload containing:
                - main_input: The main data structure (list or dict)
                - input_1, input_2, etc.: Additional data structures to merge
                - num_additional_inputs: Number of additional inputs to process
                - merge_strategy: How to handle conflicts when merging dictionaries

        Returns:
            A dictionary containing the result of the operation
        """
        request_id = payload.get("request_id", "unknown")
        logger.info(f"Processing merge data request for request_id: {request_id}")
        # Log the full payload with all keys to help debug
        logger.info(f"PAYLOAD KEYS: {list(payload.keys())}")
        logger.debug(f"Full payload for request_id {request_id}: {payload}")

        # Check if the payload has a tool_parameters field (from the API component)
        if "tool_parameters" in payload and isinstance(payload["tool_parameters"], dict):
            logger.info(f"Found 'tool_parameters' field in payload. Using it for parameters.")
            # Use the tool_parameters as the actual payload
            parameters = payload["tool_parameters"]
            # Keep the request_id from the original payload
            parameters["request_id"] = request_id
        else:
            # Use the original payload
            parameters = payload

        logger.info(f"PARAMETERS KEYS: {list(parameters.keys())}")

        try:
            # Get inputs from parameters
            main_input = parameters.get("main_input")
            if main_input is None:
                raise KeyError("Required field 'main_input' not found in parameters")

            # Get number of additional inputs
            try:
                num_additional_inputs = int(parameters.get("num_additional_inputs", 2))
                num_additional_inputs = min(num_additional_inputs, self.MAX_ADDITIONAL_INPUTS)
            except (ValueError, TypeError):
                logger.warning(f"Invalid num_additional_inputs, using default value of 2")
                num_additional_inputs = 2

            # Get merge strategy
            merge_strategy = parameters.get("merge_strategy", "Overwrite")

            # Log input values for debugging
            logger.info(f"Merging data for request_id {request_id}. Strategy: '{merge_strategy}', Num additional inputs: {num_additional_inputs}")
            logger.debug(f"Main input type: {type(main_input).__name__}")

            # Handle Structured Compose strategy differently
            if merge_strategy == "Structured Compose":
                # For structured compose, create a new dictionary with custom keys
                result = {}

                # Add main input with first key
                output_key_1 = parameters.get("output_key_1", "data_1")
                result[output_key_1] = copy.deepcopy(main_input)
                logger.info(f"Added main input as '{output_key_1}' for request_id {request_id}")

                # Add additional inputs with their respective keys
                for i in range(1, num_additional_inputs + 1):
                    input_name = f"input_{i}"
                    input_value = parameters.get(input_name)

                    if input_value is not None:
                        # Get the corresponding output key (output_key_2 for input_1, output_key_3 for input_2, etc.)
                        output_key_name = f"output_key_{i + 1}"
                        output_key_value = parameters.get(output_key_name, f"data_{i + 1}")
                        
                        result[output_key_value] = copy.deepcopy(input_value)
                        logger.info(f"Added {input_name} as '{output_key_value}' for request_id {request_id}")

                logger.info(f"Structured compose completed for request_id {request_id} with keys: {list(result.keys())}")
                return {
                    "status": "success",
                    "result": result
                }

            # Initialize the result with the main input for other strategies
            result = copy.deepcopy(main_input)
            input_type = type(main_input)

            # Process additional inputs
            for i in range(1, num_additional_inputs + 1):
                input_name = f"input_{i}"
                input_value = parameters.get(input_name)

                # Skip None or empty inputs
                if input_value is None:
                    logger.debug(f"Skipping None input for {input_name}")
                    continue

                # Check if types are compatible
                if not isinstance(input_value, input_type):
                    if isinstance(main_input, list) and isinstance(input_value, list):
                        # Both are lists, so it's fine
                        pass
                    elif isinstance(main_input, dict) and isinstance(input_value, dict):
                        # Both are dicts, so it's fine
                        pass
                    else:
                        # Types are not compatible
                        error_msg = (
                            f"Cannot merge data of types {type(result).__name__} and {type(input_value).__name__} "
                            f"for input {input_name}. All inputs must be of the same type (list or dict)."
                        )
                        logger.error(error_msg)
                        return {
                            "status": "error",
                            "error": error_msg
                        }

                # Merge based on type
                if isinstance(result, list) and isinstance(input_value, list):
                    # Merge lists by concatenation
                    try:
                        result = result + input_value
                        logger.info(f"List {input_name} merged successfully for request_id {request_id}. Current length: {len(result)}")
                    except Exception as e:
                        error_msg = f"Error merging list {input_name} for request_id {request_id}: {str(e)}"
                        logger.error(error_msg)
                        return {
                            "status": "error",
                            "error": error_msg
                        }

                elif isinstance(result, dict) and isinstance(input_value, dict):
                    # Merge dictionaries based on strategy
                    try:
                        if merge_strategy == "Overwrite":
                            # Shallow merge (update with new dict)
                            result.update(copy.deepcopy(input_value))
                            logger.info(
                                f"Dictionary {input_name} merged with overwrite strategy for request_id {request_id}. Current keys: {list(result.keys())}"
                            )

                        elif merge_strategy == "Deep Merge":
                            # Deep merge (recursively merge nested dictionaries)
                            result = self._deep_merge(result, input_value)
                            logger.info(
                                f"Dictionary {input_name} merged with deep merge strategy for request_id {request_id}. Current keys: {list(result.keys())}"
                            )

                        elif merge_strategy == "Aggregate":
                            # Aggregate merge (combine conflicting values into a list)
                            result = self._aggregate_merge(result, input_value)
                            logger.info(
                                f"Dictionary {input_name} merged with aggregate strategy for request_id {request_id}. Current keys: {list(result.keys())}"
                            )

                        elif merge_strategy == "Error on Conflict":
                            # Check for conflicts
                            conflicts = [key for key in input_value if key in result]
                            if conflicts:
                                error_msg = f"Key conflicts detected with {input_name} for request_id {request_id}: {conflicts}"
                                logger.error(error_msg)
                                return {
                                    "status": "error",
                                    "error": error_msg
                                }

                            # No conflicts, safe to merge
                            result.update(copy.deepcopy(input_value))
                            logger.info(
                                f"Dictionary {input_name} merged with no conflicts for request_id {request_id}. Current keys: {list(result.keys())}"
                            )

                        else:
                            # This should be caught by validation, but included for safety
                            error_msg = f"Unknown merge strategy for request_id {request_id}: {merge_strategy}"
                            logger.error(error_msg)
                            return {
                                "status": "error",
                                "error": error_msg
                            }

                    except Exception as e:
                        error_msg = f"Error merging dictionary {input_name} for request_id {request_id}: {str(e)}"
                        logger.error(error_msg)
                        return {
                            "status": "error",
                            "error": error_msg
                        }

            # Return the final merged result
            logger.info(f"All data merged successfully for request_id {request_id}")
            return {
                "status": "success",
                "result": result
            }

        except Exception as e:
            error_msg = f"Unexpected error processing merge data request for request_id {request_id}: {str(e)}"
            logger.error(error_msg)
            logger.debug(f"Exception details for request_id {request_id}: {traceback.format_exc()}")
            return {
                "status": "error",
                "error": error_msg
            }

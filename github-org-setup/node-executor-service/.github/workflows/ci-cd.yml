# GitHub Actions CI/CD Pipeline for Node Executor Service
# This replaces the GitLab CI configuration

name: Node Executor Service CI/CD

on:
  push:
    branches:
      - main
      - dev
      - staging
  workflow_dispatch:

jobs:
  ci-cd:
    name: CI/CD Pipeline
    # Only run on specific branches (matching GitLab CI rules)
    if: github.ref_name == 'main' || github.ref_name == 'dev' || github.ref_name == 'staging'
    
    # Use the team-aware pipeline from the templates repository
    uses: your-org/github-actions-templates/.github/workflows/team-pipeline.yml@main
    
    with:
      # Service configuration
      service_name: "node-executor-service"
      team: "ruh-catalyst" # This service belongs to the ruh-catalyst team
      
      # Resource configurations (matching your GitLab CI setup)
      memory_request: "200Mi"
      cpu_request: "100m"
      memory_limit: "600Mi"
      cpu_limit: "250m"
      container_port: "50052" # gRPC port as per your original config
      service_port: "80"
      
      # Optional: Custom ingress host (if not provided, will use default pattern)
      # ingress_host: 'node-executor-ruh-catalyst-${{ github.ref_name }}.rapidinnovation.dev'
    
    secrets:
      # Global secrets
      git_token: ${{ secrets.GIT_TOKEN }}
      
      # Team-specific secrets (ruh-catalyst team)
      team_service_account_key: ${{ secrets.RUH_CATALYST_SERVICE_ACCOUNT_KEY }}
      team_slack_webhook: ${{ secrets.RUH_CATALYST_SLACK_WEBHOOK }}

# This workflow will:
# 1. Build Docker image: us-central1-docker.pkg.dev/{PROJECT_ID}/ruh-catalyst/node-executor-service:{BRANCH}-{SHA}
# 2. Deploy to namespace: ruh-catalyst-{branch} (e.g., ruh-catalyst-dev, ruh-catalyst-staging, ruh-catalyst-main)
# 3. Create Kubernetes resources:
#    - ServiceAccount: node-executor-service-sa
#    - Deployment: node-executor-service-dp
#    - Service: node-executor-service-svc
#    - Ingress: node-executor-service-ingress
# 4. Send Slack notifications to ruh-catalyst team channel
#
# The deployment will use the same resource limits and configurations as your GitLab CI setup.
# The namespace pattern changes from 'ruh-{branch}' to 'ruh-catalyst-{branch}' to support multiple teams.

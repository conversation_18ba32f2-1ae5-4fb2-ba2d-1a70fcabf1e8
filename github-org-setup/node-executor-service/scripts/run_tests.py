"""
Test runner for the node-executor-service.
"""

import pytest
import sys

if __name__ == "__main__":
    # Add arguments to run specific tests if provided, otherwise run all tests
    args = sys.argv[1:] if len(sys.argv) > 1 else ["tests/"]
    
    # Add verbose flag and show locals on failure
    args = ["-v", "--showlocals"] + args
    
    # Run pytest with the specified arguments
    sys.exit(pytest.main(args))
